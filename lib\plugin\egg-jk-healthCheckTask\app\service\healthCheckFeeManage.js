/*
 * @Author: mj
 * @Description: 体检合同费用管理
 */

const { Service } = require('egg');
const moment = require('moment');

class HealthCheckFeeManageService extends Service {
    // 获取费用列表
    async getFeesList(query) {
        try {
            const { ctx } = this;
            const { pageNum = 1, pageSize = 10, companyName, tjDate, physicalOrgId } = query;
            const match = {
                'contractInfo.physicalOrgId': physicalOrgId
            };
            if (companyName) {
                match['enterpriseInfo.cname'] = { $regex: companyName, $options: 'i' };
            }
            if (tjDate) {
                const startDate = moment(tjDate[0]).startOf('day').toDate();
                const endDate = moment(tjDate[1]).endOf('day').toDate();
                match['contractInfo.examStartDate'] = { $gte: startDate };
                match['contractInfo.examEndDate'] = { $lte: endDate };
            }

            const aggregation = [
                {
                    $match: {
                        contractID: { $exists: true, $ne: null, $ne: '' },
                        EnterpriseID: { $exists: true, $ne: null, $ne: '' },
                    }
                },

                // 关联企业信息
                {
                    $lookup: {
                        from: 'adminorgs', // 注意大小写
                        localField: 'EnterpriseID',
                        foreignField: '_id',
                        as: 'enterpriseInfo'
                    }
                },
                { $unwind: '$enterpriseInfo' },

                // // 关联合同信息
                {
                    $lookup: {
                        from: 'MedicalExamContracts', // 注意大小写
                        localField: 'contractID',
                        foreignField: '_id',
                        as: 'contractInfo'
                    }
                },
                { $unwind: '$contractInfo' },

                { $match: match },

                // 分组统计
                {
                    $group: {
                        _id: '$contractID',
                        cname: { $first: '$enterpriseInfo.cname' },
                        examStartTime: { $first: '$contractInfo.examStartDate' },
                        examEndTime: { $first: '$contractInfo.examEndDate' },
                        totalPrice: { $sum: '$totalPrice' },
                        peopleCount: { $sum: 1 },
                        receivedAmount: { $first: '$contractInfo.receivedAmount' }
                    }
                },

                // 格式化输出
                {
                    $project: {
                        contractID: '$_id',
                        cname: 1,
                        examStartTime: 1,
                        examEndTime: 1,
                        totalPrice: 1,
                        peopleCount: 1,
                        receivedAmount: 1,
                        // _id: 0
                    }
                },

                // 排序（按体检开始时间倒序）
                { $sort: { examStartTime: -1 } },

                {
                    $facet: {
                        data: [
                            { $skip: Number((pageNum - 1)) * Number(pageSize) },
                            { $limit: Number(pageSize) }
                        ],
                        total: [
                            { $count: 'total' }
                        ]
                    }
                },
                { $unwind: '$total' },
                {
                    $project: {
                        data: 1,
                        total: '$total.total'
                    }
                },
            ];

            const res = await ctx.model.HealthCheckRegister.aggregate(aggregation)
            if (res.length === 0) {
                return {
                    data: [],
                    pagination: {
                        pageNum: parseInt(pageNum),
                        pageSize: parseInt(pageSize),
                    },
                    total: 0
                }
            } else {
                return {
                    data: res[0].data,
                    pagination: {
                        pageNum: parseInt(pageNum),
                        pageSize: parseInt(pageSize),
                    },
                    total: res[0].total
                }
            }

        } catch (error) {
            console.log(error);
        }
    }

    // 获取合同下已登记人员列表
    async getPersonListByContractId(query) {
        try {
            const { ctx } = this;
            let { pageNum = 1, pageSize = 10, name, idNumber } = query    // 查询条件
            pageNum = Number(pageNum)
            pageSize = Number(pageSize)
            let filter = {}
            filter.contractID = query.contractID
            if (name) { filter.name = { $regex: name, $options: 'i' } } // 姓名模糊查询
            if (idNumber) { filter.idNumber = { $regex: idNumber, $options: 'i' } } // 身份证号模糊查询
            const employees = await ctx.model.HealthCheckRegister.find(filter)   // 获取人员列表（分页）
                .select('checkNo name idNumber contractID totalPrice phone registerTime')
                .sort({ registerTime: 1 })
                .skip((pageNum - 1) * pageSize)
                .limit(pageSize)
                .lean()

            // 获取总人数
            const total = await ctx.model.HealthCheckRegister.find(filter)

            return {
                data: employees,
                total: total.length
                // pagination: {
                //     pageNum: parseInt(pageNum),
                //     pageSize: parseInt(pageSize),
                //     total
                // }
            }
        } catch (error) {
            console.log(error)
        }
    }

    // 获取合同下体检费用明细
    async getFeeDetailByContractId(query) {
        const { ctx } = this;
        const { pageNum = 1, pageSize = 10 } = query
    }


    // 获取合同附件
    async getContractFile(query) {
        const { ctx } = this
        let searchCondition = {}
        if (query.contractID) {
            searchCondition._id = { _id: query.contractID };
        }
        const res = await ctx.model.MedicalExamContract.findOne(searchCondition).select('contractFile')
        return res
    }
}

module.exports = HealthCheckFeeManageService; 