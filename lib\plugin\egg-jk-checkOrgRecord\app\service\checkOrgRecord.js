const Service = require("egg").Service;
// 操作日志
const moment = require("moment");
class CheckOrgRecordService extends Service {
  async addRecord(params) {

    const { ctx } = this;
    let res;
    if (params.currentId) {
      res = await ctx.model.CheckRecord.findOneAndUpdate(
        { _id: params.currentId },
        { $set: params.data },
        { returnDocument: "after" }
      );
    }
    if (!params.currentId) {

      params.data.org_id = ctx.session.physicalExamUserInfo.EnterpriseID;
      params.data.org_regAdd = ctx.session.physicalExamUserInfo.regAddr
      res = await ctx.model.CheckRecord.create(params.data);
    }
    return res;
  }

  async deleteRecord(params) {
    const { ctx } = this;
    const res = await ctx.model.CheckRecord.deleteMany({
      _id: { $in: params.ids },
    });
    return res;
  }

  //getRecordList
  async getRecordList(params) {
    const { ctx } = this;
    const res = await ctx.model.CheckRecord.find({
      $or: [{ temporarilySave: true }, { submit: true }, { recordStatus: '补正备案材料'}],
      org_id: ctx.session.physicalExamUserInfo.EnterpriseID
    })
      .skip((Number(params.currentPage) - 1) * Number(params.pageSize))
      .limit(Number(params.pageSize));

    const total = await ctx.model.CheckRecord.countDocuments();

    return {
      res,
      total,
    };
  }

  async getOneRecord(params) {
    const { ctx } = this;
    const res = await ctx.model.CheckRecord.findOne({
      _id: params.id,
    });
    return res;
  }

  async saveRecord(params) {
    const { ctx } = this;
    const res = await ctx.model.CheckRecord.updateOne(
      {
        _id: params.id,
      },
      { $set: { temporarilySave: true } }
    );
    return res;
  }

  async submitRecord(params) {
    const { ctx } = this;
    const res = await ctx.model.CheckRecord.updateOne(
      {
        _id: params.id,
      },
      { $set: { submit: true, recordStatus: '待审核' } }
    );
    return res;
  }

  // cancelRecord
  async cancelRecord(params) {
    const { ctx } = this;
    const res = await ctx.model.CheckRecord.updateOne(
      {
        _id: params.id,
      },
      { $set: { submit: false, temporarilySave: true } }
    );
    return res;
  }

  async addTechnician(params) {
    const { ctx } = this;
    const res = await ctx.model.Technician.create(params);
    return res;
  }

  async getTechnicianList(params) {
    const { ctx } = this;
    const res = await ctx.model.Technician.find({
      checkRecordId: params.id,
    });
    return res;
  }

  async removeTechnician(params) {
    const { ctx } = this;
    const res = await ctx.model.Technician.deleteOne({
      _id: params.id,
    });
    return res;
  }

  async addInstrument(params) {
    const { ctx } = this;
    const res = await ctx.model.InspectionInstrument.create(params);
    return res;
  }

  async getInstruments(params) {
    const { ctx } = this;
    const res = await ctx.model.InspectionInstrument.find({
      checkRecordId: params.checkRecordId,
    })
      .skip((Number(params.currentPage) - 1) * Number(params.pageSize))
      .limit(Number(params.pageSize));

    const total = await ctx.model.InspectionInstrument.countDocuments({
      checkRecordId: params.id,
    });

    return {
      list: res,
      total,
    };
  }

  async removeInstrument(params) {
    const { ctx } = this;
    const res = await ctx.model.InspectionInstrument.deleteOne({
      _id: params.id,
    });
    return res;
  }

  async downRecordTable(params) { 
    const { ctx } = this;
    const { checkRecordId } = params;
    const orgData = await ctx.model.CheckRecord.findOne({ _id: checkRecordId }).lean();
    const { checkType } = orgData;
    const technicianData = await ctx.model.Technician.find({ checkRecordId }).lean();
    const inspectionInstrument = await ctx.model.InspectionInstrument.find({ checkRecordId }).lean();
    const updatedTechnicianData = technicianData.map(tech => {
      const remarks = [];
      if (tech.practicingNumber) {
        remarks.push(`医师资格证书编号：${tech.practicingNumber}`);
      }
      if (tech.qualificationNumber) {
        remarks.push(`职业病诊断培训合格证书号：${tech.qualificationNumber}`);
      }
      return {
        ...tech,
        remark: remarks.length > 0 ? remarks.join('\n') : undefined
      };
    });
    const allCategories = [
      "接触粉尘类",
      "接触化学因素类",
      "接触物理因素类",
      "接触生物因素类",
      "接触放射因素类",
      "其他类(特殊作业等)",
    ];
    const unit = [
      '国有医院',
      '事业单位',
      '企业医院',
      '民营',
    ];
    const unitNature = unit.map((label) => ({
      label,
      selected: orgData.unitNature === label
    }));
    const selectedMap = new Map();
    checkType.forEach(([group, child]) => {
      if (!selectedMap.has(group)) {
        selectedMap.set(group, new Set());
      }
      selectedMap.get(group).add(child);
    });
    const chineseNumerals = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十'];
    const options = [
      {
        value: '接触粉尘类',
        label: '接触粉尘类',
        children: [
          { value: '游离二氧化硅粉尘', label: '游离二氧化硅粉尘' },
          { value: '煤尘（煤砂尘）', label: '煤尘（煤砂尘）' },
          { value: '石棉粉尘', label: '石棉粉尘' },
          { value: '其他致尘肺病的无机粉尘', label: '其他致尘肺病的无机粉尘' },
          { value: '棉尘（亚麻、软大麻、黄麻粉尘）', label: '棉尘（亚麻、软大麻、黄麻粉尘）' },
          { value: '有机粉尘', label: '有机粉尘' }
        ]
      },
      {
        value: '接触化学因素类',
        label: '接触化学因素类',
        children: [
          { value: '铅及其无机化合物', label: '铅及其无机化合物' },
          { value: '四乙基铅', label: '四乙基铅' },
          { value: '汞及其无机化合物', label: '汞及其无机化合物' },
          { value: '锰及其无机化合物', label: '锰及其无机化合物' },
          { value: '铍及其无机化合物', label: '铍及其无机化合物' },
          { value: '镉及其无机化合物', label: '镉及其无机化合物' },
          { value: '铬及其无机化合物', label: '铬及其无机化合物' },
          { value: '氧化锌', label: '氧化锌' },
          { value: '砷', label: '砷' },
          { value: '砷化氢（砷化三氢）', label: '砷化氢（砷化三氢）' },
          { value: '磷及其无机化合物', label: '磷及其无机化合物' },
          { value: '磷化氢', label: '磷化氢' },
          { value: '钡化合物（氯化钡、硝酸钡、醋酸钡）', label: '钡化合物（氯化钡、硝酸钡、醋酸钡）' },
          { value: '钒及其无机化合物', label: '钒及其无机化合物' },
          { value: '三烷基锡', label: '三烷基锡' },
          { value: '铊及其无机化合物', label: '铊及其无机化合物' },
          { value: '羟基银', label: '羟基银' },
          { value: '氟及其无机化合物', label: '氟及其无机化合物' },
          { value: '苯', label: '苯' },
          { value: '甲苯', label: '甲苯' },
          { value: '二甲苯', label: '二甲苯' },
          { value: '二硫化碳', label: '二硫化碳' },
          { value: '四氯化碳', label: '四氯化碳' },
          { value: '甲醇', label: '甲醇' },
          { value: '溴甲烷', label: '溴甲烷' },
          { value: '1，2—二氯乙烷', label: '1，2—二氯乙烷' },
          { value: '正己烷', label: '正己烷' },
          { value: '苯的氨基与硝基化合物', label: '苯的氨基与硝基化合物' },
          { value: '三硝基甲苯', label: '三硝基甲苯' },
          { value: '联苯胺', label: '联苯胺' },
          { value: '氯气', label: '氯气' },
          { value: '二氧化硫', label: '二氧化硫' },
          { value: '氮氧化物', label: '氮氧化物' },
          { value: '氨', label: '氨' },
          { value: '光气', label: '光气' },
          { value: '甲醛', label: '甲醛' },
          { value: '一甲胺', label: '一甲胺' },
          { value: '一氧化碳', label: '一氧化碳' },
          { value: '硫化氢', label: '硫化氢' },
          { value: '氯乙烯', label: '氯乙烯' },
          { value: '三氯乙烯', label: '三氯乙烯' },
          { value: '氯丙烯', label: '氯丙烯' },
          { value: '氯丁二烯', label: '氯丁二烯' },
          { value: '有机氟', label: '有机氟' },
          { value: '二异氰酸甲苯酯', label: '二异氰酸甲苯酯' },
          { value: '二甲基甲酰胺', label: '二甲基甲酰胺' },
          { value: '氰及腈类化合物', label: '氰及腈类化合物' },
          { value: '酚类', label: '酚类' },
          { value: '五氯酚', label: '五氯酚' },
          { value: '氯甲醚[双(氯甲基) 醚参照执行]', label: '氯甲醚[双(氯甲基) 醚参照执行]' },
          { value: '丙烯酰胺', label: '丙烯酰胺' },
          { value: '偏二甲基肼', label: '偏二甲基肼' },
          { value: '硫酸二甲酯', label: '硫酸二甲酯' },
          { value: '有机磷杀虫剂', label: '有机磷杀虫剂' },
          { value: '氨基甲酸酯类杀虫剂', label: '氨基甲酸酯类杀虫剂' },
          { value: '拟除虫菊酯类', label: '拟除虫菊酯类' },
          { value: '酸雾或酸酐', label: '酸雾或酸酐' },
          { value: '致喘物', label: '致喘物' },
          { value: '焦炉逸散物', label: '焦炉逸散物' }
        ]
      },
      {
        value: '接触物理因素类',
        label: '接触物理因素类',
        children: [
          { value: '噪声', label: '噪声', checked: true },
          { value: '手传振动', label: '手传振动' },
          { value: '高温', label: '高温', checked: true },
          { value: '高气压', label: '高气压', checked: true },
          { value: '紫外辐射（紫外线）', label: '紫外辐射（紫外线）', checked: true },
          { value: '微波', label: '微波' }
        ]
      },
      {
        value: '接触生物因素类',
        label: '接触生物因素类',
        children: [
          { value: '布鲁菌属', label: '布鲁菌属' },
          { value: '炭疽芽孢杆菌（炭疽杆菌）', label: '炭疽芽孢杆菌（炭疽杆菌）' }
        ]
      },
      {
        value: '接触放射因素类',
        label: '接触放射因素类',
        children: [
          { value: '外照射', label: '外照射' },
          { value: '内照射', label: '内照射' }
        ]
      },
      {
        value: '其他类（特殊作业等）',
        label: '其他类（特殊作业等）',
        children: [
          { value: '电工作业', label: '电工作业' },
          { value: '高处作业', label: '高处作业' },
          { value: '压力容器作业', label: '压力容器作业' },
          { value: '结核病防治工作', label: '结核病防治工作' },
          { value: '肝炎病防治工作', label: '肝炎病防治工作' },
          { value: '职业机动车驾驶作业', label: '职业机动车驾驶作业' },
          { value: '视屏作业', label: '视屏作业' },
          { value: '高原作业', label: '高原作业' },
          { value: '航空作业', label: '航空作业' },
          { value: '可能导致井下工人滑囊炎的作业', label: '可能导致井下工人滑囊炎的作业' }
        ]
      }
    ];
    const checkItems = options.map((group, index) => ({
      groupName: `${chineseNumerals[index]}、${group.label}`,
      children: group.children.map(child => ({
        childName: child.label,
        checked: checkType.some(
          ([parent, childLabel]) => parent === group.label && childLabel === child.label
        )
      }))
    }));
    const checkedCategoriesSet = new Set(checkType.map(item => item[0]));
    orgData.selectNum = checkedCategoriesSet.size;
    const checkedCategories = allCategories.map((label, index) => ({
      label: `${chineseNumerals[index]}、${label}`,
      selected: checkedCategoriesSet.has(label)
    }));
    // 当前时间
    orgData.year = moment().format('YYYY');
    orgData.month = moment().format('MM');
    orgData.day = moment().format('DD');
    const data = {
      orgData,
      updatedTechnicianData,
      deviceData: inspectionInstrument,
      checkItems,
      checkedCategories,
      unitNature,
    };
    const res = await ctx.helper.fillWord(ctx, '职业健康检查机构备案登记表', data);
    return res.path;
  }
}

module.exports = CheckOrgRecordService;
