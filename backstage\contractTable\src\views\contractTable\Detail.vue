<template>
    <div class="tj-container">
        <div class="return" @click="goBack">
            <el-button type="primary" size="mini" plain>返回</el-button>
            <!-- <button>< 返回</button><span>体检合同列表</span> -->
        </div>
        <TitleTag :titleName="'查询条件'"></TitleTag>
        <div class="contractTable">
            <el-row>
                <el-col :span="24">
                    <div class="first-line">
                        <div class="search-item">
                            <span class="label-title">员工姓名</span>
                            <el-input placeholder="请输入姓名" style="margin:5px 5px;width: 200px;"
                                v-model.trim="filterVal.name" size="mini" suffix-icon="el-icon-search">
                            </el-input>
                        </div>

                        <div class="search-item">
                            <span class="label-title">身份证号</span>
                            <el-input placeholder="请输入身份证号" style="margin:5px 5px;width: 200px;"
                                v-model.trim="filterVal.idNumber" size="mini" suffix-icon="el-icon-search">
                            </el-input>
                        </div>

                        <div class="search-item i-select">
                            <span class="label-title">预约状态</span>
                            <el-select v-model="filterVal.reservationStatu" size="mini" placeholder="请选择">
                                <el-option v-for="item in reservationOptions" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </div>

                    
                    

                        <div class="search-item">
                            <span class="label-title">联系方式</span>
                            <el-input placeholder="请输入联系方式" style="margin:5px 5px;width: 200px;"
                                v-model="filterVal.contactPhone" size="mini" suffix-icon="el-icon-search">
                            </el-input>
                        </div>

                        <div class="search-item">
                            <el-button type="primary"  plain size="mini" style="margin-left: 10px;" @click="handleSearch"
                                icon="el-icon-search">查询</el-button>
                            <el-button type="warning"  plain size="mini" @click="handleReset"
                                icon="el-icon-refresh-right">重置</el-button>
                        </div>
                    </div>
                </el-col>
            </el-row>
        </div>
        <TitleTag :titleName="'人员列表'"></TitleTag>
        <div class="contractTable">
            <div class="handleBtn">
                <el-button type="primary" size="mini" @click="batchReason('批量同意')" plain >批量同意</el-button>
                <el-button type="danger" size="mini" plain @click="batchReason('批量拒绝')">批量拒绝</el-button>
            </div>
            <div class="table">
                <el-table :data="tableData" row-key="_id" stripe
                    :header-cell-style="{ background: '#F5F7FA', fontSize: '14px', fontWeight: 700, color: '#333' }"
                    @selection-change="selectionChange">
                    <el-table-column type="selection" width="55"></el-table-column>
                    <el-table-column prop="name" label="姓名" align="center" min-width="120"
                        show-overflow-tooltip></el-table-column>
                    <el-table-column prop="contactPhone" label="联系方式" align="center" min-width="120"></el-table-column>
                    <el-table-column prop="idNumber" label="身份证号" align="center" min-width="120"
                        show-overflow-tooltip></el-table-column>
                    <!-- <el-table-column label="体检类别" align="center" min-width="120">
                        <template slot-scope="scope">
                            <div v-if="scope.row.contractType === 0">离岗</div>
                            <div v-else-if="scope.row.contractType === 1">岗前</div>
                            <div v-else-if="scope.row.contractType === 2">在岗</div>
                        </template>
</el-table-column> -->
                    <el-table-column label="预约时间" align="center" width="120">
                        <template slot-scope="scope">
                            {{ processDate(scope.row.reservationDate) }}
                        </template>
                    </el-table-column>
                    <el-table-column label="预约状态" align="center" min-width="120">
                        <template slot-scope="scope">
                            <el-tag type="warning" size="mini" v-if="scope.row.reservationStatu === 0">未预约</el-tag>
                            <el-tag type="primary" size="mini" v-if="scope.row.reservationStatu === 1">已预约</el-tag>
                            <el-tag type="success" size="mini" v-if="scope.row.reservationStatu === 2">已同意</el-tag>
                            <el-tag type="danger" size="mini" v-if="scope.row.reservationStatu === 3">已拒绝</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="contractPeopleNum" label="操作" align="left" fixed="right" width="175">
                        <template slot-scope="scope">
                            <el-button type="success" size="mini" plain @click="clickToView(scope.row)">查看</el-button>
                            <el-button type="primary" size="mini" @click="refuseReservation('同意',scope.row._id)"
                                v-show="scope.row.reservationStatu === 1"
                                plain>同意</el-button>
                            <el-button type="danger" size="mini" plain
                                v-show="scope.row.reservationStatu === 1"
                                @click="refuseReservation('拒绝', scope.row._id)">拒绝</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="pagination">
                <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                    :current-page="filterVal.currentPage" :page-size="filterVal.pageSize"
                    :page-sizes="[10, 20, 30, 50, 100]" layout="total, sizes, prev, pager, next, jumper" :total="total">
                </el-pagination>
            </div>
        </div>
        <!-- 弹框 -->
        <el-dialog top="5%" width="1000px" title="查看合同签订详情" :visible.sync="dialogVisible">
            <div>
                <el-form :model="detailedData" label-position="right" label-width="150px">
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="姓名" prop="name">
                                <el-input v-model="detailedData.name" placeholder="请输入姓名" disabled></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="联系方式" prop="contactPhone">
                                <el-input v-model="detailedData.contactPhone" placeholder="请输入联系方式" disabled></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="身份证号" prop="idNumber">
                                <el-input v-model="detailedData.idNumber" placeholder="请输入身份证号" disabled></el-input>
                            </el-form-item>
                        </el-col>
                        <!-- <el-col :span="12">
                    <el-form-item label="体检类别" prop="contractType">
                        <el-select v-model="detailedData.contractType" placeholder="请选择体检类别" disabled>
                        <el-option label="岗前" :value=1></el-option>
                        <el-option label="在岗" :value=2></el-option>
                        <el-option label="离岗" :value=0></el-option>
                        </el-select>
                    </el-form-item>
                </el-col> -->
                    </el-row>
                    <!-- <el-row>
                <el-col :span="12">
                    <el-form-item label="预约状态" prop="reservationStatu">
                        <el-select v-model="detailedData.reservationStatu" placeholder="请选择体检类别" disabled>
                        <el-option label="已预约" :value=2></el-option>
                        <el-option label="未预约" :value=1></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
          
            </el-row> -->
                </el-form>
            </div>
            <div class="el-grant-the-staff-foot footer-distance">
                <el-button @click="confirm" size="middle" type="primary">关闭</el-button>
            </div>
        </el-dialog>
        <!-- 拒绝弹框 -->
         <el-dialog :visible.sync="reasonDialog" title="拒绝原因" @close="closeDialog">
            <div>拒绝原因：
                <el-input 
                    placeholder="请输入拒绝原因"
                    v-model="statusForm.refuseReason" 
                    type="textarea" 
                    show-word-limit
                    maxlength="200"
                    autosize
                    />
                </div>
            <template #footer>
                <el-button type="primary" @click="confirmReveraion">确定</el-button>
                <el-button @click="closeDialog">取消</el-button>
            </template>
         </el-dialog>
    </div>
</template>

<script>
import TitleTag from '@/components/TitleTag.vue';
import { getPersonDetail, medicalExamBook } from '@/api/index';
import moment from 'moment';
export default {
    components: {
        TitleTag
    },
    data() {
        return {
            reasonDialog: false,
            dialogVisible: false,
            statusForm: {
                id: undefined,
                reservationStatu: undefined,
                refuseReason: ''
            },
            detailedData: {

                name: '',
                contactPhone: '',
                idNumber: '',
                contractType: null,
                reservationStatu: null,
            },
            tableData: [],
            total: 0,
            filterVal: {
                name: '',
                idNumber: '',
                contactPhone: '',
                contractType: null,
                reservationStatu: null,          // 0为未预约,1为已预约
                pageSize: 10,
                pageNum: 1,
                contractId: this.$route.query.contractId
            },
            reservationOptions: [
                { label: '未预约', value: 0 },
                { label: '已预约', value: 1 },
                { label: '已同意', value: 2 },
                { label: '已拒绝', value: 3 },
            ],
            tjTypeOptions: [
                { label: '离岗', value: 0 },
                { label: '岗前', value: 1 },
                { label: '在岗', value: 2 },
            ],
            id: null,
        }
    },
    created() {
        this.initData();
    },
    methods: {
        selectionChange(arr) {
            if (arr.length===0) {
                this.statusForm.id = undefined
            } else {
                this.statusForm.id = arr.map(item => item._id)
            }
        },
        goBack() {
            this.$router.go(-1);
        },
        handleSizeChange(val) {
            this.filterVal.pageSize = val
            this.initData()
        },
        handleCurrentChange(val) {
            this.filterVal.pageNum = val
            this.initData()
        },
        processDate(dateString) {
            // 使用moment来格式化日期
            return moment(dateString).format('YYYY-MM-DD');
        },
        async initData() {
            const data = await getPersonDetail(this.filterVal)
            this.tableData = data.data.res
            this.total = data.data.total
        },
        async agreeReservation(id) {
            await medicalExamBook({
                reservationStatu: 1,
                id: '677b86b638920c60a9683d61',
            })
        },
        closeDialog() {
            this.statusForm.refuseReason = ''
            this.statusForm.id = undefined
            this.statusForm.reservationStatu = undefined
            this.reasonDialog = false
        },
        async confirmReveraion() {
            if(!this.statusForm.refuseReason) return this.$message({type: 'warning', message: '请填写拒绝原因'});
            this.editStatusResponse()
        },
        async refuseReservation(title, id) {
            switch (title) {
                case '同意':
                    this.statusForm.reservationStatu = 2
                    this.statusForm.id = id
                    this.editStatusResponse()
                    break;
                case '拒绝':
                    this.reasonDialog = true
                    this.statusForm.reservationStatu = 3
                    this.statusForm.id = id
                    break;
            }
        },
        async batchReason(title) {
            if(!this.statusForm.id) {
                return this.$message({type: 'warning', message: '至少选择一项数据'})
            } else {
                if (title ==='批量拒绝') {
                    this.reasonDialog = true
                    this.statusForm.reservationStatu = 3
                } else {
                    this.statusForm.reservationStatu = 2
                    delete this.statusForm.refuseReason
                    this.editStatusResponse()
                }
            }
        },
        async clickToView(item) {
            this.dialogVisible = true
            this.detailedData = item

        },
        confirm() {
            this.dialogVisible = false
        },
        confirmAdd(val) {
            console.log('确认新增', val)
        },
        handleSearch() {
            this.initData()
        },
        handleReset() {
            Object.keys(this.filterVal).forEach(str => {
                if(str!=='pageNum' && str!=='pageSize' && str !=='contractId') {
                    this.filterVal[str] = ''
                }
            })

            this.initData()
        },
        async editStatusResponse() {
            await medicalExamBook(this.statusForm)
            this.$message({type: 'success', message: `${this.statusForm.reservationStatu==2?'同意':'拒绝'}成功`})
            this.initData();
            this.reasonDialog = false
        },
    }
}
</script>

<style lang="scss" scoped>
.tj-container {
    padding: 20px 20px;
}

.table {
    margin-top: 15px;
}

.pagination {
    margin-top: 20px;
    display: flex;
    justify-content: center;
}

::v-deep .el-input {
    width: 250px;
}

::v-deep .el-date-editor.el-input {
    width: 250px;
}

::v-deep .el-date-editor .el-range-input {
    margin-left: 15px;
}

::v-deep .custom-input .el-input__inner {
    border: none;
    height: 24px;
    line-height: 24px;
    text-align: center;
}


.custom-input {
    font-size: 12px;
    color: #606266;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    display: inline-block;
    margin-left: 6px;
    margin-right: 6px;
    height: 28px;
    line-height: 28px;
    padding: 0 0;
    box-sizing: border-box;
}

.contractTable {
    position: relative;

    .handleBtn {
        position: absolute;
        right: 0;
        top: -58px;
    }
}

.first-line {
    display: flex;
    flex-wrap: wrap;

    .label-title {
        text-align: right;
        display: inline-block;
        width: 80px;
        // background-color: red;
    }

    .label-title-uni {
        text-align: right;
        display: inline-block;
        width: 140px;
    }

    .search-item {
        display: flex;
        align-items: center;
    }
}

::v-deep .i-select .el-input {
    width: 200px;
    margin: 5px 5px;
}

.footer-distance {
    text-align: right;
    margin-top: 15px !important;
}

::v-deep .el-tag {
    border-radius: 28px;
}
</style>