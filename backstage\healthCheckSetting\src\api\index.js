import request from '@root/publicMethods/request';
// 新增体检检查项目
export function addNewHealthItem(data){
  return request({
    url: '/manage/healthCheckTask/addNewHealthItem',
    data,
    method: 'post',
  })
}

// 获取检查项目列表
export function getCheckItemList(params){
  return request({
    url: '/manage/healthCheckTask/getHealthItemList',
    params,
    method: 'get',
  })
}

// 更改检查项目信息
export function editHealthItem(data){
  return request({
    url: '/manage/healthCheckTask/editHealthItem',
    data,
    method: 'post',
  })
}

// 获取结果类型选项
export function getResultTypeOption(params){
  return request({
    url: '/api/healthCheckTask/getResultTypeOption',
    params,
    method: 'get',
  })
}

// 获取检查项目单位选项
export function getHealthCheckUnit(params){
  return request({
    url: '/api/healthCheckTask/getHealthCheckUnit',
    params,
    method: 'get',
  })
}

// 删除职业健康检查项目
export function deleteHealthItem(data){
  return request({
    url: '/manage/healthCheckTask/deleteHealthItem',
    data,
    method: 'post',
  })
}

// 批量导入健康检查项目
export function batchImportHealthItems(data){
  return request({
    url: '/manage/healthCheckTask/batchImportHealthItems',
    data,
    method: 'post',
  })
}
