<template>
  <div class="serveEnterprise">
    <TitleTag titleName="查询条件"></TitleTag>
    <div class="search-container">
      <el-form :inline="true" :model="formSearch" class="demo-form-inline">
        <el-form-item label="劳动者姓名">
          <el-input v-model="formSearch.name" placeholder="请输入劳动者姓名"></el-input>
        </el-form-item>
        <el-form-item label="证件号码">
          <el-input v-model="formSearch.idNumber" placeholder="请输入证件号码"></el-input>
        </el-form-item>
        <el-form-item label="联系电话">
          <el-input v-model="formSearch.phone" placeholder="请输联系电话"></el-input>
        </el-form-item>
        <el-form-item label="体检类别">
          <el-select v-model="formSearch.examType" placeholder="请选择">
            <el-option v-for="item in tjTypeOption" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="检查机构名称">
          <el-input v-model="formSearch.insititutionName" placeholder="请输入检查机构名称"></el-input>
        </el-form-item> -->
        <el-form-item>
          <el-button type="primary" @click="onSubmit" icon="el-icon-search">查询</el-button>
        </el-form-item>
        <el-form-item>
          <el-button @click="reset" icon="el-icon-refresh">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <TitleTag titleName="报告列表"></TitleTag>
    <div class="table">
      <el-table :data="tableData" tooltip-effect="light" style="width: 100%" stripe border
        header-cell-style="background-color: #f5f7fa; color: #606266;height:46px">
        <el-table-column type="index" width="50" label="序号" align="center"></el-table-column>
        <el-table-column label="姓名" align="center" prop="name" show-overflow-tooltip></el-table-column>
        <el-table-column label="身份证号" align="center" prop="idNumber" min-width="130">
          <template slot-scope="scope">
            {{ scope.row.idNumber | handletoIdNumber }}
          </template>
        </el-table-column>
        <el-table-column label="联系电话" align="center" prop="phone">
          <template slot-scope="scope">
            {{ scope.row.phone | handletoPhone }}
          </template>
        </el-table-column>
        <el-table-column label="体检类别" align="center" prop="tjTypeName" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ getExamType(scope.row.examType) }}
          </template>
        </el-table-column>
        <el-table-column label="检查机构名称" align="center" prop="physicalOrgName" min-width="200"
          show-overflow-tooltip></el-table-column>
        <el-table-column label="检查日期" align="center" prop="registerTime" width="120">
          <template slot-scope="scope">
            {{ getFormatDate(scope.row.detectTime) }}
          </template>
        </el-table-column>
        <el-table-column label="审核状态" align="center" prop="status2" width="100">
          <template slot-scope="scope">
            <span :class="getStatusClass(scope.row.status)">{{ getStatusText(scope.row.status) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="查看详情" align="center" width="160" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" :disabled="scope.row.status > 2" @click="showAuditDialog(scope.row)">审核</el-button>
            <el-button type="text" style="color:#67C23A;" @click="reportDetail(scope.row)">查看报告</el-button>
            <!-- <el-button type="text" v-if="scope.row.isAgreeHistoryReport" style="color:#909399;"
              @click="reportDetail(scope.row)">查看历史报告</el-button> -->
            <!-- <el-button type="text" style="color:#E6A23C;" @click="applyHistoryReport(scope.row)">申请查看历史报告</el-button> -->
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination">
      <el-pagination @size-change="getList" @current-change="getList" :current-page.sync="pageInfo.pageNum"
        :page-size.sync="pageInfo.pageSize" :page-sizes="[10, 20, 30, 50, 100]" background
        layout="total, sizes, prev, pager, next, jumper" :total="total">
      </el-pagination>
    </div>
    <SuspectDiseaseDetail :show.sync="isShowDetail" :itemData="currentData" />

    <!-- 审核弹窗 -->
    <el-dialog title="审核" :visible.sync="auditDialogVisible" width="500px" :close-on-click-modal="false">
      <el-form :model="auditForm" :rules="auditRules" ref="auditForm" label-width="100px">
        <el-form-item label="审核状态" prop="status">
          <el-radio-group v-model="auditForm.status">
            <el-radio :label="3">通过</el-radio>
            <el-radio :label="4">不通过</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核原因" prop="reason">
          <el-input type="textarea" :rows="4" placeholder="请输入审核原因" v-model="auditForm.reason">
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="auditDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitAudit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getHCReportList, reportReview } from '@/api'
import SuspectDiseaseDetail from '../components/SuspectDiseaseDetail.vue';
import TitleTag from '../components/TitleTag.vue';
import moment from 'moment';
export default {
  components: {
    SuspectDiseaseDetail,
    TitleTag
  },
  data() {
    return {
      total: null,
      pageInfo: {
        pageNum: 1,
        pageSize: 10
      },
      tjTypeOption: [
        { label: '上岗', value: 1 },
        { label: '在岗', value: 2 },
        { label: '离岗', value: 3 }
      ],
      isShowDetail: false,
      currentData: null,
      formSearch: {
        name: ''
      },
      auditDialogVisible: false,
      auditForm: {
        status: 1,
        reason: '',
        id: null
      },
      tableData:[],
      auditRules: {
        status: [
          { required: true, message: '请选择审核状态', trigger: 'change' }
        ],
        reason: [
          { required: true, message: '请输入审核原因', trigger: 'blur' }
        ]
      }
    }
  },
  async created() {
    await this.getList({ ...this.pageInfo })
  },

  methods: {
    async getList(params = {}) {
      const res = await getHCReportList(params)
      this.tableData = res.data.list
      this.total = this.tableData.length
    },

    reportDetail(row) {
      console.log(row)
      this.$router.push({ path: '/admin/tjHealthCheckReportManage/detail', query: { id: row.id } })
    },
    applyHistoryReport(item) {
      this.$confirm('此操作需要劳动者同意, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        console.log(item)
        this.$message({
          type: 'success',
          message: '申请成功,劳动者同意后可继续查看历史体检报告。'
        });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        });
      })
    },
    getDetail(row) {
      this.currentData = row
      this.isShowDetail = true
    },

    onSubmit() {
      this.getList({ ...this.pageInfo, ...this.formSearch })
    },
    reset() {
      this.formSearch = this.$options.data()['formSearch']
      this.tableData = this.$options.data()['tableData']
      this.getList({ ...this.pageInfo })
    },

    // 
    getExamType(val) {
      // 1: 岗前, 2: 在岗, 3: 离岗
      switch (val) {
        case '1':
          return '岗前'
        case '2':
          return '在岗'
        case '3':
          return '离岗'
        default:
          return '未知'
      }
    },

    getFormatDate(val) {
      return moment(val).format('YYYY-MM-DD')
    },

    getStatusText(status) {
      switch (status) {
        case 2:
          return '待审核'
        case 3:
          return '通过'
        case 4:
          return '不通过'
        default:
          return '未知'
      }
    },

    getStatusClass(status) {
      switch (status) {
        case 2:
          return 'status-pending'
        case 3:
          return 'status-success'
        case 4:
          return 'status-fail'
        default:
          return ''
      }
    },

    showAuditDialog(row) {
      this.auditForm.id = row.id;
      console.log(row)
      this.auditForm.status = 3; // 默认通过
      this.auditForm.reason = '';
      this.auditDialogVisible = true;
    },
    submitAudit() {
      this.$refs.auditForm.validate(async (valid) => {
        if (valid) {
          const res = await reportReview({
            _id: this.auditForm.id,
            status: this.auditForm.status,
            message: this.auditForm.reason
          });
          if (res.status === 200) {
            this.$message.success('审核提交成功');
            this.onSubmit();
            // 更新状态
            this.tableData = this.tableData.map(item => {
              if (item.id === this.auditForm.id) {
                item.status = this.auditForm.status;
              }
              return item;
            });
            this.auditDialogVisible = false;
          } else {
            this.$message.error('审核提交失败');
          }
        }
      });
    }
  },
  filters: {
    handletoPhone(val) {
      let reg = /^(1[3-9][0-9])\d{4}(\d{4}$)/;
      let phone = val.replace(reg, '$1****$2');
      return phone;
    },
    handletoIdNumber(val) {
      const reg = /^(\d{6})\d{8}(\d{4})$/;
      return val.replace(reg, "$1********$2");
    }
  }
}
</script>

<style lang="scss" scoped>
.serveEnterprise {
  padding: 15px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  // margin-top: 15px;
}

.num {
  font-size: 18px;
  color: #1691e0;
  font-weight: bolder;
}

.table {
  margin-top: 10px;
}

.pagination {
  margin-top: 10px;
  display: flex;
  justify-content: center;
}

::v-deep .el-table__fixed,
::v-deep .el-table__fixed-right {
  height: 100% !important;
}

.excel-upload-input {
  display: none;
  z-index: -9999;
}

.status-pending {
  color: #E6A23C;
}

.status-success {
  color: #67C23A;
}

.status-fail {
  color: #F56C6C;
}
</style>
