<template>
    <div :class="classObj">
        <div class="main-container">
            <TitleTag titleName="查询条件"></TitleTag>
            <el-form :inline="true" :model="searchForm" class="demo-form-inline">
                <el-form-item label="项目名称">
                    <el-input v-model="searchForm.projectName"></el-input>
                </el-form-item>
                <el-form-item label="项目编号">
                    <el-input v-model="searchForm.projectNumber"></el-input>
                </el-form-item>
                <el-form-item label="婚姻限制">
                    <el-select v-model="searchForm.marriage">
                        <el-option label="不限" :value="1"></el-option>
                        <el-option label="未婚" :value="2"></el-option>
                        <el-option label="已婚" :value="3"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="适用类型">
                    <el-select v-model="searchForm.suitType" placeholder="请选择适用类型" style="width:200px;">
                        <el-option label="职业健康检查项目" :value="1"></el-option>
                        <el-option label="平时体检项目" :value="2"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSubmit" icon="el-icon-search">查询</el-button>
                    <el-button @click="reset" icon="el-icon-refresh-left">重置</el-button>
                </el-form-item>
            </el-form>
            <TitleTag titleName="检查项目列表">
                <el-button icon="el-icon-plus" type="primary" size="small" @click="addHealthCheckProject">添加</el-button>
                <el-button icon="el-icon-upload2" type="primary" size="small"
                    @click="addHealthCheckProject">批量导入</el-button>
            </TitleTag>
            <div style="margin-bottom: 10px;">

            </div>
            <el-table :data="routineCheckItems" tooltip-effect="light" style="width: 100%" stripe border
                header-cell-style="background-color: #f5f7fa; color: #606266;height:46px">
                <el-table-column prop="projectName" label="项目名称" min-width="120" show-overflow-tooltip align="center" />
                <el-table-column prop="projectNumber" label="项目编号" min-width="120" show-overflow-tooltip
                    align="center" />
                <el-table-column prop="resultTypeName" label="结果类型" min-width="120" align="center"></el-table-column>
                <el-table-column prop="unitName" label="单位" min-width="120" align="center" />
                <el-table-column prop="genderLimitName" label="性别限制" min-width="100" align="center"></el-table-column>
                <el-table-column label="年龄限制" min-width="100" prop="ageLimitRange" align="center"></el-table-column>
                <el-table-column label="参考值范围" min-width="100" show-overflow-tooltip prop="standardRange"
                    align="center"></el-table-column>
                <el-table-column label="婚姻状况限制" width="110" prop="marriageName" align="center"></el-table-column>
                <el-table-column label="适用类型" width="160" prop="suitTypeName" align="center"></el-table-column>
                <el-table-column prop="createTimeFormat" label="创建时间" min-width="120" show-overflow-tooltip
                    align="center"></el-table-column>
                <el-table-column label="操作" width="220" fixed="right" align="left">
                    <template slot-scope="scope">
                        <el-button type="success" size="small" plain @click="toDetail(scope.row)"
                            icon="el-icon-view">查看</el-button>
                        <el-button type="primary" size="small" plain @click="toEdit(scope.row)"
                            icon="el-icon-edit">编辑</el-button>
                        <el-button type="danger" size="small" plain @click="toDelete(scope.row._id)"
                            icon="el-icon-delete">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination @size-change="getItemList" @current-change="getItemList"
                    :current-page.sync="pageInfo.pageNum" :page-size.sync="pageInfo.pageSize"
                    :page-sizes="[10, 20, 30, 50, 100]" background layout="total, sizes, prev, pager, next, jumper"
                    :total="total">
                </el-pagination>
            </div>
            <AddNewHealthCheckProject :show.sync="isShowAddProject" :titleName="'新增职业健康体检项目'" @confirm="confirmAdd"
                :resultTypeOptions="resultTypeOptions" :unitOption="unitOption" />
            <EditDetail :show.sync="isShowEdit" :editData="currentData" @confirm="confirmUpdate"
                :resultTypeOptions="resultTypeOptions" :unitOption="unitOption" />
            <ItemDetail :show.sync="isShowDetail" :itemData="currentData"></ItemDetail>
        </div>
    </div>
</template>
<script>
import { initEvent } from "@root/publicMethods/events";
import AddNewHealthCheckProject from '../components/AddNewHealthCheckProject.vue';
import ItemDetail from '../components/ItemDetail.vue';
import EditDetail from '../components/EditDetail.vue';
import TitleTag from '../components/TitleTag.vue'
import { addNewHealthItem, getCheckItemList, editHealthItem, getResultTypeOption, getHealthCheckUnit, deleteHealthItem } from '@/api/index'
import moment from 'moment'
export default {
    name: 'checkProjectSetting',
    components: {
        AddNewHealthCheckProject,
        ItemDetail,
        EditDetail,
        TitleTag
    },
    data() {
        return {
            sidebarOpened: true,
            isShowAddProject: false,
            pageInfo: {
                pageNum: 1,
                pageSize: 10
            },
            total: null,
            isShowDetail: false,
            isShowEdit: false,
            currentData: null,
            routineCheckItems: [],
            genderLimitOptions: [
                { label: '男', val: 2 },
                { label: '女', val: 3 },
                { label: '不限', val: 1 }
            ],
            marriageLimitOptions: [
                { label: '未婚', val: 2 },
                { label: '已婚', val: 3 },
                { label: '不限', val: 1 }
            ],
            searchForm: {
                projectName: '',
                projectNumber: '',
                marriage: null
            },
            resultTypeOptions: [],
            unitOption: []
        }
    },
    computed: {
        classObj() {
            return {
                hideSidebar: !this.sidebarOpened,
                openSidebar: this.sidebarOpened,
                withoutAnimation: "false",
                mobile: this.device === "mobile",
            }
        }
    },
    async created() {
        await this.getResultTypeOption()
        await this.getHealthCheckUnitOption()
        await this.getItemList()
    },
    mounted() {
        initEvent(this);
    },
    methods: {
        // 获取职业健康检查结果类型选项
        async getResultTypeOption() {
            const res = await getResultTypeOption()
            if (res.status === 200) {
                this.resultTypeOptions = res.data
            }
        },
        // 获取职业健康检查单位类型选项
        async getHealthCheckUnitOption() {
            const res = await getHealthCheckUnit()
            if (res.status === 200) {
                this.unitOption = res.data
            }
        },
        // 查看
        toDetail(tab) {
            this.currentData = tab
            this.isShowDetail = true
        },
        // 编辑
        toEdit(tab) {
            this.currentData = tab
            this.isShowEdit = true
        },
        async toDelete(id) {
            this.$confirm('此操作将永久删除该检查项目, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const res = await deleteHealthItem({ id })
                if (res.status === 200 && res.message === '删除成功') {
                    this.getItemList()
                    this.$message({
                        type: 'success',
                        message: '删除成功!'
                    })
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                })
            })
        },
        addHealthCheckProject() {
            this.isShowAddProject = true
        },
        async getItemList() {
            let params = {
                ...this.pageInfo,
                ...this.searchForm
            }
            const result = await getCheckItemList(params)
            if (result.status === 200 && result.data && result.data.res.length >= 0) {
                this.routineCheckItems = this.handleData(result.data.res)
                this.total = result.data.total
            }
        },
        handleData(data) {
            data.forEach(item => {
                item.genderLimitName = this.handleGender(item.genderLimit)
                item.standardRange = item.isStandard && item.isStandard == 1 ? item.standardValueMin + '~' + item.standardValueMax : ''    // 具有参考值
                item.ageLimitRange = item.isLimitAge && item.isLimitAge == 1 ? item.ageLimitMin + '~' + item.ageLimitMax + '岁' : '不限'
                item.marriageName = this.handleMarriage(item.marriage)
                if (item.suitType && item.suitType.length > 0) {
                    let suitTypeName = ''
                    item.suitType.forEach(sin => {
                        if (sin == 1) {
                            suitTypeName = suitTypeName ? suitTypeName + '/职业健康检查项目' : '职业健康检查项目'
                        }
                        if (sin == 2) {
                            suitTypeName = suitTypeName ? suitTypeName + '/平时检查项目' : '平时检查项目'
                        }
                    })
                    item.suitTypeName = suitTypeName
                }
                item.createTimeFormat = moment(item.createdAt).format('YYYY-MM-DD')
                item.updateTimeFormat = moment(item.updatedAt).format('YYYY-MM-DD')
                item.resultTypeName = this.handleResultType(item.resultType)
                item.unitName = this.handleUnit(item.msrunt)
            })
            return data
        },
        // 处理结果类型
        handleResultType(val) {
            if (!val) {
                return ''
            }
            let resultName = ''
            this.resultTypeOptions.forEach(item => {
                if (item.value == val) {
                    resultName = item.name
                }
            })
            return resultName ? resultName : val
        },
        // 处理检查项目单位
        handleUnit(val) {
            if (!val) {
                return ''
            }
            let unitName = ''
            this.unitOption.forEach(item => {
                if (item.value == val) {
                    unitName = item.name
                }
            })
            return unitName ? unitName : val
        },
        // 处理婚姻
        handleMarriage(val) {
            let limitString = ''
            this.marriageLimitOptions.forEach(item => {
                if (val && val === item.val) {
                    limitString = item.label
                }
            })
            return limitString
        },
        // 处理性别
        handleGender(val) {
            let limitString = ''
            this.genderLimitOptions.forEach(item => {
                if (val && val === item.val) {
                    limitString = item.label
                }
            })
            return limitString
        },
        async confirmAdd(val) {
            const res = await addNewHealthItem(val)
            if (res.status === 200 && res.message === '新增成功。') {
                this.$message.success('新增成功。')
                await this.getItemList()
            }
        },
        async confirmUpdate(val) {
            await editHealthItem(val)
            this.$message.success('更新成功。')
            this.getItemList()
        },
        onSubmit() {
            this.pageInfo = this.$options.data()['pageInfo']
            this.getItemList()
        },
        reset() {
            this.searchForm = this.$options.data()['searchForm']
            this.pageInfo = this.$options.data()['pageInfo']
            this.getItemList()
        }
    }
}
</script>
<style scoped>
.pagination {
    display: flex;
    justify-content: center;
    margin-top: 10px;
}

.serveEnterprise {
    margin-left: 210px;
    padding: 15px;
}

.main-container{
    padding: 15px;
}
</style>