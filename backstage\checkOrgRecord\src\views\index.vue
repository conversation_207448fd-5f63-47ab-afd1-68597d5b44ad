<template>
  <div class="checkOrgRecord">
    <el-card style="height: 92vh">
      <div class="head">
        <div>
          <span style="font-weight: bold">备案列表</span>
        </div>
        <div class="line"></div>
        <div class="opt">
          <el-button type="primary" @click="goToAdd">首次备案</el-button>
          <el-button @click="changeFiling">变更备案</el-button>
        </div>
      </div>

      <el-table
        :data="tableData"
        height="75vh"
        style="width: 100%; margin-top: 30px"
        align="center"
        :header-cell-style="{
          color: 'black',
          height: '50px',
          'background-color': '#F5F7FA',
        }"
      >
        <el-table-column
        show-overflow-tooltip
          prop="institution"
          label="机构名称"
          align="center"
          fixed="left"
        >
        </el-table-column>
        <el-table-column label="备案类别" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.recordType === '首次备案'">首次备案</span>
            <span v-else>变更备案</span>
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip prop="area" label="区域" align="center">
        </el-table-column>
        <el-table-column show-overflow-tooltip prop="address" label="单位地址" align="center">
        </el-table-column>
        <el-table-column prop="unitNature" label="单位性质" align="center">
        </el-table-column>
        <el-table-column prop="legal" label="法定代表人" align="center">
        </el-table-column>
        <el-table-column prop="job" label="职务" align="center">
        </el-table-column>
        <el-table-column prop="contacts" label="联系人" align="center">
        </el-table-column>
        <el-table-column show-overflow-tooltip prop="phoneNumber" label="联系电话" align="center">
        </el-table-column>
        <el-table-column show-overflow-tooltip prop="postcode" label="邮编" align="center">
        </el-table-column>
        <el-table-column prop="workforce" label="职工总数" align="center">
        </el-table-column>
        <el-table-column prop="practicingPhysicianTotal" label="从事职业健康检查执业医师人数" align="center">
        </el-table-column>
        <el-table-column prop="diagnosticQualificationTotal" label="取得职业病诊断资格人数" align="center">
        </el-table-column>
        <el-table-column show-overflow-tooltip prop="licenseNumber" label="医疗机构执业许可证编号" align="center">
        </el-table-column>
        <el-table-column show-overflow-tooltip prop="issueDate" label="发证日期" align="center">
        </el-table-column>
        <el-table-column show-overflow-tooltip prop="issueOrg" label="发证机关" align="center">
        </el-table-column>
        <el-table-column label="备案状态" align="center">
          <template slot-scope="scope">
             <span style="color: #229242;" v-if="'recordStatus' in scope.row && scope.row.recordStatus === '审核通过'">审核通过</span>
             <span style="color: #E43C2F;" v-else-if="'recordStatus' in scope.row && scope.row.recordStatus === '不予备案'">不予备案</span>
             <span v-else style="color:chocolate;">{{scope.row.recordStatus}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="reviewComments" label="审核意见" align="center">
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right" align="center">
          <template slot-scope="scope">
            <div style="display: flex; justify-content: space-around;">
             
            <el-link
              v-if="scope.row.submit === false || scope.row.recordStatus === '补正备案材料'"
              type="primary"
              size="small"
              @click="submit(scope.row._id)"
              >提交</el-link
            >
            <el-link
            v-if="scope.row.submit === false || scope.row.recordStatus === '补正备案材料'"
              type="primary"
              size="small"
              :disabled="scope.row.recordStatus !== '补正备案材料' && scope.row.submit"
              @click="handleEdit(scope.row._id)"
              >编辑</el-link
            >

            <el-link
              type="primary"
              size="small"
              v-if="(scope.row.recordStatus === '补正备案材料' && scope.row.submit) || (scope.row.submit && scope.row.recordStatus === '待审核')"
              @click="cancel(scope.row._id)"
              >撤回</el-link
            >
            <el-link
             v-if="scope.row.submit === false"
              size="small"
              type="danger"
              @click="handleDelete(scope.row._id)"
              >删除</el-link
            >
            </div>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        style="margin-top: 20px; display: flex; justify-content: center"
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="pageSize"
        :page-sizes="[10, 20, 30, 40, 50, 100]"
        :current-page="currentPage"
        @size-change="changeSize"
        @current-change="changePage"
      >
      </el-pagination>
    </el-card>

    <el-dialog
      title="选择备案类别"
      :visible.sync="dialogFormVisible"
      width="30%"
    >
      <el-select style="width: 100%;" v-model="recordType" placeholder="请选择备案类别">
        <el-option label="首次备案" value="首次备案"></el-option>
        <el-option label="变更备案" value="变更备案"></el-option>
      </el-select>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmAdd">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getRecordList, submitRecord, cancelRecord, deleteRecord, getRecordInfo } from "@/api";
export default {
  data() {
    return {
      dialogFormVisible: false,
      recordType: "",
      tableData: [],
      pageSize: 10,
      currentPage: 1,
      total: 0,
      ids: [],
    };
  },
  async created() {
    this.search();
  },
  methods: {
    async changeFiling() {
      const res = await getRecordInfo()
      if(res.data.first === null || res.data.first.length === 0) {
        this.$message.warning('请先进行首次备案')
        return
      }
      if(res.data.status === null || res.data.status.length === 0) {
        this.$router.push({
          name: "add",
          params: {
            recordType: '变更备案',
          },
        });
      } else {
        this.$message.warning('存在待审核的备案')
        return
      }

    },
    async handleDelete(id) {
      const res = await deleteRecord({
        ids: [id],
      });

      if (res.status === 200) {
        this.$message.success("已删除");
        this.search();
      }
    },
    handleEdit(id) {
      this.$router.push({
        name: "edit",
        params: {
          id,
        },
      });
    },
    async cancel(id) {
      const res = await cancelRecord({
        id,
      });
      if (res.status === 200) {
        this.$message.success("已撤回");
        this.search();
      }
    },
    async submit(id) {
      const res2 = await submitRecord({
        id,
      });
      if (res2.status === 200) {
        this.$message.success("已提交");
        this.search();
      }
    },
    async search() {
      const res = await getRecordList({
        pageSize: this.pageSize,
        currentPage: this.currentPage,
      });

      this.tableData = res.data.res;
      this.total = res.data.total;
    },
    async goToAdd() {
      const res = await getRecordInfo()
      if(res.data.first === null || res.data.first.length === 0) {
        this.$router.push({
          name: "add",
          params: {
            recordType: '首次备案',
          },
        });
      } else {
        this.$message.warning('已有首次备案')
        return
      }

    },
    confirmAdd() {
     
    },
    changeSize(val) {
      this.pageSize = val;
      this.search();
    },
    changePage(val) {
      this.currentPage = val;
      this.search();
    },
  },
  components: {},
};
</script>

<style scoped lang="scss">
.head {
  display: flex;
  height: 30px;
  align-items: center;
  border-left: 10px solid #409eff;
  justify-content: space-between;
  padding-left: 10px;
  .line {
    flex: 3;
    margin-left: 10px;
    border-top: 1.5px solid #ccc;
  }
  .opt {
    margin-left: 10px;
  }
}
</style>
