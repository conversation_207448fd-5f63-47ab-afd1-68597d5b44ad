<template>
  <div>
    <input
      ref="excel-upload-input"
      class="excel-upload-input"
      type="file"
      accept=".xlsx, .xls"
      @change="handleClick"
    />
    <!-- <div class="drop" @drop="handleDrop" @dragover="handleDragover" @dragenter="handleDragover">
      拖曳 excel 文件 或 -->
    <div class="btnDv">
      <div>
        <el-button
          :loading="loading"
          type="primary"
          @click="handleUpload"
          size="small"
          class="importExcel"
        >
          导入
        </el-button>
        <el-button size="small" @click="download" class="downloadBtn">模板下载</el-button>
        <slot></slot>
      </div>
      <div>
        <slot name="rightBtn"></slot>
        <el-button
          :loading="loading"
          size="small"
          type="primary"
          @click="submit"
          class="submitBtn"
        >
          保存
        </el-button>
      </div>
    </div>

    <!-- </div> -->
  </div>
</template>

<script>
import XLSX from "xlsx";
export default {
  props: {
    beforeUpload: Function, // eslint-disable-line
    onSuccess: Function, // eslint-disable-line,
    submit: Function,
    multipleSelection: Array,
    rightBtnText: {
      type: String,
      default: "保存",
    },
  },
  data() {
    return {
      loading: false,
      excelData: {
        header: null,
        results: null,
      },
    };
  },
  created() {
    console.log("导入危害因素=====");
  },
  methods: {
    deleteMany() {
      this.$emit("deleteMany");
    },
    generateData({ header, results }) {
      this.excelData.header = header;
      this.excelData.results = results;
      this.onSuccess && this.onSuccess(this.excelData);
    },
    handleDrop(e) {
      e.stopPropagation();
      e.preventDefault();
      if (this.loading) return;
      const files = e.dataTransfer.files;
      if (files.length !== 1) {
        this.$message.error("Only support uploading one file!");
        return;
      }
      const rawFile = files[0]; // only use files[0]
      if (!this.isExcel(rawFile)) {
        this.$message.error("Only supports upload .xlsx, .xls, .csv suffix files");
        return false;
      }
      this.upload(rawFile);
      e.stopPropagation();
      e.preventDefault();
    },
    handleDragover(e) {
      e.stopPropagation();
      e.preventDefault();
      e.dataTransfer.dropEffect = "copy";
    },
    handleUpload() {
      this.$refs["excel-upload-input"].click();
    },
    handleClick(e) {
      const files = e.target.files;
      const rawFile = files[0]; // only use files[0]
      if (!rawFile) return;
      this.upload(rawFile);
    },
    upload(rawFile) {
      this.$refs["excel-upload-input"].value = null; // fix can't select the same excel
      if (!this.beforeUpload) {
        this.readData(rawFile);
        return;
      }
      const before = this.beforeUpload(rawFile);
      if (before) {
        this.readData(rawFile, true, true);
      }
    },
    readData(rawFile, fillMergedCells = false, fillEmptyCell = false) {
      this.loading = true;
      return new Promise((resolve) => {
        const reader = new FileReader();
        reader.onload = (e) => {
          const data = e.target.result;
          // const workbook = XLSX.read(data, { type: 'array' })
          const workbook = XLSX.read(data, { type: "array", cellDates: true });
          const firstSheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[firstSheetName];
          const header = this.getHeaderRow(worksheet);

          const results = XLSX.utils.sheet_to_json(worksheet, { defval: "" });
          this.generateData({ header, results });
          this.loading = false;
          resolve();
        };
        reader.readAsArrayBuffer(rawFile);
      });

      // this.loading = true
      // return new Promise((resolve, reject) => {
      //   const reader = new FileReader()
      //   reader.onload = e => {
      //     const data = e.target.result
      //     const workbook = XLSX.read(data, { type: 'array' })
      //     const firstSheetName = workbook.SheetNames[0]
      //     const worksheet = workbook.Sheets[firstSheetName]
      //     if(fillMergedCells){
      //       this.fillMergedRange(worksheet, fillEmptyCell);
      //     }
      //     const header = this.getHeaderRow(worksheet)
      //     const results = XLSX.utils.sheet_to_json(worksheet)
      //     this.generateData({ header, results })
      //     this.loading = false
      //     resolve()
      //   }
      //   reader.readAsArrayBuffer(rawFile)
      // })
    },
    fillMergedRange(sheet, isFillEmptyCell = false) {
      //Get all merged ranges
      if (!sheet["!merges"]) {
        if (isFillEmptyCell)
          // this.fillEmptyCell(sheet);
          return;
      }
      for (let mRange of sheet["!merges"].values()) {
        let sS = XLSX.utils.encode_cell(mRange.s);
        //Get the start cell value
        let sVal = sheet[sS];
        //Fill all cells in the merged range, s=start cell, e=end cell
        for (let c = mRange.s.c; c <= mRange.e.c; c++) {
          for (let r = mRange.s.r; r <= mRange.e.r; r++) {
            let sCell = XLSX.utils.encode_cell({ c: c, r: r });
            sheet[sCell] = sVal;
          }
        }
      }
      //Clean up the merged array in worksheet
      delete sheet["!merges"];
      // if(isFillEmptyCell)
      //   this.fillEmptyCell(sheet);
    },
    fillEmptyCell(sheet) {
      if (!sheet["!ref"]) return;
      const range = XLSX.utils.decode_range(sheet["!ref"]);
      //Check and fill column by column
      //from first column to last column
      for (let c = range.s.c; c <= range.e.c; c++) {
        let lastVal;
        //from first row to last row
        for (let r = range.s.r; r <= range.e.r; r++) {
          let sCell = XLSX.utils.encode_cell({ c: c, r: r });
          if (sheet[sCell]) lastVal = sheet[sCell];
          else sheet[sCell] = lastVal;
        }
      }
    },
    getHeaderRow(sheet) {
      const headers = [];
      const range = XLSX.utils.decode_range(sheet["!ref"]);
      let C;
      const R = range.s.r;
      /* start in the first row */
      for (C = range.s.c; C <= range.e.c; ++C) {
        /* walk every column in the range */
        const cell = sheet[XLSX.utils.encode_cell({ c: C, r: R })];
        /* find the cell in the first row */
        let hdr = "UNKNOWN " + C; // <-- replace with your desired default
        if (cell && cell.t) hdr = XLSX.utils.format_cell(cell);
        headers.push(hdr);
      }
      return headers;
    },
    isExcel(file) {
      return /\.(xlsx|xls|csv)$/.test(file.name);
    },
    download() {
      this.$emit("downloadTemplate");
    },
  },
};
</script>

<style scoped>
.excel-upload-input {
  display: none;
  z-index: -9999;
}
.drop {
  border: 2px dashed #bbb;
  width: 400px;
  height: 100px;
  line-height: 100px;
  margin: 0 0 50px;
  font-size: 24px;
  border-radius: 5px;
  text-align: center;
  color: #bbb;
  position: relative;
}
.btnDv {
  display: flex;
  justify-content: space-between;
  padding: 0px 0 20px;
}
</style>
