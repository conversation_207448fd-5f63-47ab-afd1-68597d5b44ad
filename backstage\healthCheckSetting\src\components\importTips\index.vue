<template>
  <div>
    <el-dialog
      title="导入失败"
      :visible="dialogVisible"
      width="60%"
      :before-close="handleClose">
      <div v-if="requireError.length>0"> <i style="color: #F56C6C" class="el-icon-warning"></i>以下选项未正确填写
          <div style="color: #F56C6C;margin-top:8px"><span style="color: #F56C6C">
            {{requireError.join('；')}}。
        </span>
      </div>
      </div>
      <div style="color: #F56C6C;margin-top:8px" v-if="touchTimeErr">{{touchTimeErr}}</div>
      <!-- <div style="margin-top: 10px"><span style="color: #F56C6C">*</span>最新模板<el-link type="primary" :href="excelTemplateUrl">点击下载</el-link></div> -->
      <div style="margin-top: 10px; line-height: 22px;"><strong>提示</strong>：如若导入失败请检查您的 Excel <span style="color: #E6A23C">表头是否符合要求</span>，请勿修改模板表头文字，可修改表头顺序；必填项填写完整；危害因素填写正确，如有疑义或需要新增危害因素，<span style="color: #E6A23C">请联系在线客服。</span></div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleClose">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: ['dialogVisible', 'touchTimeErr','requireError'],
  // computed: {
  //   excelTemplateUrl() {
  //     return localStorage.resultExcelTemp
  //   }
  // },
  data() {
    return {
      excelTemplateUrl:''
    }
  },
  // created(){
    
  //   this.excelTemplateUrl = localStorage.resultExcelTemp
  // },
  methods: {
    handleClose() {
      this.$emit('update:dialogVisible', false)
    }
  }
}
</script>

<style lang="scss" scoped>

</style>