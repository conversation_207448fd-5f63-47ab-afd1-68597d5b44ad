// 体检人员预约
module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;
  const medicalExamPlanSchema = new Schema(
    {
      _id: {
        type: String,
        default: shortid.generate,
      },
      name: { // 姓名
        type: String,
      },
      contactPhone: { // 联系方式
        type: String,
      },
      idNumber: { // 身份证号
        type: String,
      },
      examType: { // 体检类别 0离岗 1岗前 2在岗
        type: Number,
        enum: [0, 1, 2],
      },
      reservationStatu: { // 预约状态 0未预约 1已预约(待审核) 2审核通过 3已拒绝
        type: Number,
        enum: [0, 1, 2, 3],
      },
      contractId: { // 对应的合同id
        type: String,
        ref: 'MedicalExamContract',
      },
      EnterpriseId: { // 企业id
        type: String,
        ref: 'Adminorg',
      },
      employeeId: { // 员工id
        type: String,
        ref: 'Employees',
      },
      refuseReason: { // 拒绝原因
        type: String,
        default: '',
      },
      reservationDate: { // 预约时间
        type: String,
      },
      registerCheckNo: { // 体检登记，通过体检登记表的checkNo关联
        type: String,
      }
    },
    {
      timestamps: true,
    }
  );
  // 不主动populate不会包含在结果
  medicalExamPlanSchema.virtual('healthCheckRegister', {
    ref: 'HealthCheckRegister',
    localField: 'registerCheckNo',
    foreignField: 'checkNo',
    justOne: true
  });
  medicalExamPlanSchema.set('toObject', { virtuals: true });
  medicalExamPlanSchema.set('toJSON', { virtuals: true });

  return mongoose.model('MedicalExamPlan', medicalExamPlanSchema, 'MedicalExamPlans');
};
