/*
 * @Author: lcz
 * @Description: 体检登记控制器
 */

const HealthCheckRegisterController = {
  // 获取体检登记列表
  async getRegisterList(ctx) {
    try {
      const body = ctx.request.body;
      body.physicalOrgID = ctx.session.physicalExamUserInfo.EnterpriseID;
      const result = await ctx.service.healthCheckRegister.getRegisterList(body);
      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '获取成功',
        status: 200,
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, { message: error.message });
    }
  },

  // 创建体检登记
  async createRegister(ctx) {
    try {
      const data = ctx.request.body;
      const result = await ctx.service.healthCheckRegister.createRegister(data);
      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '创建成功'
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, { message: error.message });
    }
  },

  // 更新体检登记
  async updateRegister(ctx) {
    try {
      const data = ctx.request.body;
      const { _id } = data;
      if (!_id) {
        throw new Error('缺少参数_id');
      }
      const result = await ctx.service.healthCheckRegister.updateRegister(data);
      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '更新成功'
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, { message: error.message });
    }
  },

  // 删除体检登记
  async deleteRegister(ctx) {
    try {
      const { _id } = ctx.query;
      if (!_id) {
        throw new Error('缺少参数_id');
      }
      await ctx.service.healthCheckRegister.deleteRegister(_id);
      ctx.helper.renderSuccess(ctx, { message: '删除成功' });
    } catch (error) {
      ctx.helper.renderFail(ctx, { message: error.message });
    }
  },

  // 获取体检登记详情
  async getRegisterDetail(ctx) {
    try {
      const { _id } = ctx.query;
      if (!_id) {
        throw new Error('缺少参数_id');
      }
      const result = await ctx.service.healthCheckRegister.getRegisterDetail(_id);
      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '获取成功'
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, { message: error.message });
    }
  },

  async getRegisterDetailByCheckNo(ctx) {
    try {
      const { checkNo } = ctx.query;
      console.log(ctx.query, '==========ctx.query🍊')
      if (!checkNo) {
        throw new Error('缺少参数checkNo');
      }
      const result = await ctx.service.healthCheckRegister.getRegisterDetailByCheckNo(checkNo);
      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '获取成功'
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, { message: error.message });
    }
  },

  async getRegisterDetailByIdNumber(ctx) {
    try {
      const { idNumber } = ctx.query;
      if (!idNumber) {
        throw new Error('缺少参数idNumber');
      }
      const params = ctx.query;
      params.physicalOrgID = ctx.session.physicalExamUserInfo.EnterpriseID;
      const result = await ctx.service.healthCheckRegister.getRegisterDetailByIdNumber(params);
      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '获取成功'
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, { message: error.message });
    }
  },

  // 获取科室和收费项目列表
  async getDepartmentAndProjectList(ctx) {
    try {
      const pyload = ctx.query;
      const physicalOrgId = ctx.session.physicalExamUserInfo.EnterpriseID;
      pyload.physicalOrgId = physicalOrgId;
      const result = await ctx.service.healthCheckRegister.getDepartmentAndProjectList(pyload);
      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '获取成功'
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, { message: error.message });
    }
  },

  //获取当日登记人员列表
  async getTodayRegisterList(ctx) {
    try {
      const physicalOrgId = ctx.session.physicalExamUserInfo.EnterpriseID;
      const result = await ctx.service.healthCheckRegister.getTodayRegisterList(physicalOrgId);
      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '获取成功'
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, { message: error.message });
    }
  },

  // 结果录入
  async inputResult(ctx) {
    try {
      const data = ctx.request.body;
      await ctx.service.healthCheckRegister.inputResult(data);
      ctx.helper.renderSuccess(ctx, { message: '录入成功' });
    } catch (error) {
      ctx.helper.renderFail(ctx, { message: error.message });
    }
  },

  // 科室小结
  async departmentSummary(ctx) {
    try {
      const data = ctx.request.body;
      await ctx.service.healthCheckRegister.departmentSummary(data);
      ctx.helper.renderSuccess(ctx, { message: '小结成功' });
    } catch (error) {
      ctx.helper.renderFail(ctx, { message: error.message });
    }
  },

  // 检查总结
  async healthCheckSummary(ctx) {
    try {
      const data = ctx.request.body;
      await ctx.service.healthCheckRegister.healthCheckSummary(data);
      ctx.helper.renderSuccess(ctx, { message: '总结成功' });
    } catch (error) {
      ctx.helper.renderFail(ctx, { message: error.message });
    }
  },

  // 获取体检报告列表 
  async getHCReportList(ctx) {
    try {
      const body = ctx.request.body;
      body.physicalOrgID = ctx.session.physicalExamUserInfo.EnterpriseID;
      const result = await ctx.service.healthCheckRegister.getHCReportList(body);
      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '获取成功',
        status: 200,
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, { message: error.message });
    }
  },

  // 获取体检报告详情
  async getHCReportDetail(ctx) {
    try {
      const { _id } = ctx.query;
      if (!_id) {
        throw new Error('缺少参数_id');
      }
      const result = await ctx.service.healthCheckRegister.getHCReportDetail(_id);
      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '获取成功',
        status: 200,
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, { message: error.message });
    }
  },

  // 报告审核
  async reportReview(ctx) {
    try {
      const data = ctx.request.body;
      await ctx.service.healthCheckRegister.reportReview(data);
      ctx.helper.renderSuccess(ctx, { message: '审核成功' });
    } catch (error) {
      ctx.helper.renderFail(ctx, { message: error.message });
    }
  },

  // 查询危害因素列表详情信息
  async findHarmFactors(ctx) {
    try {
      const data = await ctx.service.healthCheckRegister.findHarmFactors(ctx.request.query);
      ctx.helper.renderSuccess(ctx, {
        data,
      });
    } catch (error) {
      console.log(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },

  async occupationalDiseaseList(ctx) {
    try {
      // const data = await ctx.service.healthCheckRegister.occupationalDiseaseList(ctx.request.query);
      const data = await ctx.model.OccupationalDisease.find();
      ctx.helper.renderSuccess(ctx, {
        data,
      });
    } catch (error) {
      console.log(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },

  async occupationalContraindicationList(ctx) {
    try {
      // const data = await ctx.service.healthCheckRegister.occupationalContraindicationList(ctx.request.query);
      const data = await ctx.model.OccupationalContraindication.find();
      ctx.helper.renderSuccess(ctx, {
        data,
      });
    } catch (error) {
      console.log(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },

  // #region 统计
  // 按照年度统计
  async getStatisticsByYear(ctx) {
    try {
      const paload = ctx.query;
      // paload.superUserInfo = ctx.session.superUserInfo;
      paload.physicalOrgID = ctx.session.physicalExamUserInfo.EnterpriseID;
      const result = await ctx.service.healthCheckRegister.getStatisticsByYear(paload);
      ctx.helper.renderSuccess(ctx, { data: result, message: '获取成功' });
    } catch (error) {
      ctx.helper.renderFail(ctx, { message: error.message });
    }
  },

  // 按照年度统计每种危害因素
  async getStatisticsByYearAndHarmFactor(ctx) {
    try {
      const paload = ctx.query;
      // paload.superUserInfo = ctx.session.superUserInfo;
      paload.physicalOrgID = ctx.session.physicalExamUserInfo.EnterpriseID;
      const result = await ctx.service.healthCheckRegister.getStatisticsByYearAndHarmFactor(paload);
      ctx.helper.renderSuccess(ctx, { data: result, message: '获取成功' });
    } catch (error) {
      ctx.helper.renderFail(ctx, { message: error.message });
    }
  },

  // 查询危害因素列表
  async findHarmFactors2(ctx) {
    try {
      const data = await ctx.service.healthCheckRegister.findHarmFactors2(ctx.request.query);
      ctx.helper.renderSuccess(ctx, {
        data,
      });
    } catch (error) {
      console.log(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },
  // #endregion

};

module.exports = HealthCheckRegisterController; 