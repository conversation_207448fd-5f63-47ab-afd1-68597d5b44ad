const path = require('path');
const fs = require('fs');

module.exports = appInfo => {
  return {
    session: {
      key: 'DUOPU_SESS',
      maxAge: 24 * 3600 * 1000, // 1 day
      httpOnly: true,
      encrypt: true,
      renew: true, // 延长会话有效期
    },

    China: {
      area_code: '100000',
      name: '中国',
    }, // 全国行政辖区，district中添加则不规范。

    // 登录有效时间
    superUserMaxAge: 1000 * 60 * 30, // 30min   后台登录可用，此框架暂为加入后台登录
    // 设置网站图标
    siteFile: {
      '/favicon.ico': fs.readFileSync(
        path.join(appInfo.baseDir, 'app/public/favicon.ico')
      ),
    },
    // gzip压缩
    compress: {
      threshold: 2048,
    },
    middleware: [
      'notfoundHandler',
      'crossHeader',
      'compress',
      'authAdminEnabel',
      'authAdminToken',
      'authAdminPower',
    ],
    // api跨域
    crossHeader: {
      match: ['/api'],
    },
    // 后台token校验
    authAdminToken: {
      match: ['/manage', '/admin', '/qy', '/user'],
    },
    // 后台权限校验
    authAdminPower: {
      match: ['/manage'],
    },
    // 文件上传
    multipart: {
      fields: '100',
      fileSize: '200mb',
      mode: 'stream',
      whiteList: [
        '.jpg',
        '.jpeg', // image/jpeg
        '.png', // image/png, image/x-png
        '.gif', // image/gif
        '.bmp', // image/bmp
        '.mp4',
        '.avi',
      ],
      fileExtensions: [
        '.doc',
        '.docx',
        '.pdf',
        '.doc',
        '.docx',
        'xlsx',
        'mp4',
        'avi',
        '.xlsx',
        '.jfif',
      ], // 扩展几种上传的文件格式
    },

    phyReportType: ['.pdf', '.docx', '.doc'],

    physicalCompanyRouter: {
      match: [ctx => ctx.path.startsWith('/manage/physicalCompany'), ctx => ctx.path.startsWith('/api/physicalCompany')],
    },

    healthCheckTaskRouter: {
      match: [
        ctx => ctx.path.startsWith('/manage/healthCheckTask'), ctx => ctx.path.startsWith('/api/healthCheckTask'),
        ctx => ctx.path.startsWith('/manage/healthCheckAppointment'),
        ctx => ctx.path.startsWith('/manage/healthCheckRegister'),
        ctx => ctx.path.startsWith('/manage/computedItems'),
        ctx => ctx.path.startsWith('/manage/healthCheckFeeManages'),
      ],
    },

    clientinfoRouter: {
      match: [ctx => ctx.path.startsWith('/manage/clientinfo'), ctx => ctx.path.startsWith('/api/clientinfo')],
      // match: [
      //   (ctx) => ctx.path.startsWith('/manage/physicalCompany'),
      //   (ctx) => ctx.path.startsWith('/api/physicalCompany'),
      // ],
    },
    operateLogRouter: {
      // 操作日志
      match: [(ctx) => ctx.path.startsWith('/manage/operateLog')],
    },
    waringAndSupervisionRouter: {
      match: [
        (ctx) => ctx.path.startsWith('/manage/warning'),
        (ctx) => ctx.path.startsWith('/api/warning'),
        (ctx) => ctx.path.startsWith('/manage/supervision'),
        (ctx) => ctx.path.startsWith('/api/supervision'),
      ],
    },

    uploadFileRouter: {
      uploadFileFormat: {
        upload_path: process.cwd() + '/app/public',
        static_root_path: 'cms', // 针对云存储可设置
      },
      match: [ctx => ctx.path.startsWith('/manage/uploadFile'), ctx => ctx.path.startsWith('/api/upload/files'), ctx => ctx.path.startsWith('/api/upload/ueditor')],
    },

    healthCheckRouter: {
      match: [ctx => ctx.path.startsWith('/manage/healthcheck')],
      // match: [
      //   (ctx) => ctx.path.startsWith('/manage/uploadFile'),
      //   (ctx) => ctx.path.startsWith('/api/upload/files'),
      //   (ctx) => ctx.path.startsWith('/api/upload/ueditor'),
      // ],
    },

    checkOrgRecordRouter: {
      match: [ctx => ctx.path.startsWith('/manage/checkOrgRecord')],
    },


    physicalExaminationAppointmentRouter: {
      match: [ctx => ctx.path.startsWith('/manage/checkItems'), ctx => ctx.path.startsWith('/manage/appointPeopleCount'), ctx => ctx.path.startsWith('/manage/defaultAppointPeopleCount')],
      // match: [
      //   (ctx) => ctx.path.startsWith('/manage/checkItems'),
      //   (ctx) => ctx.path.startsWith('/manage/appointPeopleCount'),
      //   (ctx) => ctx.path.startsWith('/manage/defaultAppointPeopleCount'),
      // ],
    },

    // 用户自行上传文件的文件系统目录，代码拼接: upload_path + /{yyyy}{mm}{dd}/ + 文件名
    upload_path: process.cwd() + '/app/public/upload/images',
    // 上行用户自行上传文件的http路径， 代码拼接：/static + upload_http_path + /{yyyy}{mm}{dd}/ + 文件名
    upload_http_path: '/upload/images',

    // 用户自行上传体检报告目录，代码拼接: upload_report_path + /体检机构ID/ + 文件名
    upload_report_path: process.cwd() + '/app/public/upload/tjReport',
    // 上行用户自行上传体检报告的http路径， 代码拼接：/static + upload_http_report_path + /体检机构ID/ + 文件名
    upload_http_report_path: '/upload/tjReport',

    // 用户自行上传体检报告目录，代码拼接: upload_report_path + /体检机构ID/ + 文件名
    upload_record_path: process.cwd() + '/app/public/upload/record',
    // 上行用户自行上传体检报告的http路径， 代码拼接：/static + upload_http_report_path + /体检机构ID/ + 文件名
    upload_http_record_path: '/upload/record',

    report_template_path: process.cwd() + '/app/public/reportTemplate',
    enterprise_path: process.cwd() + '/app/public/enterprise',
    enterprise_http_path: '/enterprise',

    // 检索代号：#0001  后台管理根目录
    admin_base_path: '/admin',
    qy_base_path: '/qy',
    user_base_path: '/user',

    // 用于绑定用户表单中各用户类型的ID
    groupID: {
      operateGroupID: 'E1XjEmqA', // 超级管理员，运营用户角色ID
      superGroupID: 'RMwIEyjWK', // 政府用户角色ID
      adminGroupID: 'vGEfBpfsv', // 企业用户角色ID
      enterpriseGroupID: 'VKXcNqm8T', // 企业端集团用户角色ID
      serviceGroupID: 'e4Qf2ic6-', // 机构用户角色ID
      jcqlcGroupID: '8e5D3G8s3', // 机构VIP(全流程)用户角色ID
      physicalExamGroupID: 'zHLKFCyXD', // 体检用户角色ID
      userGroupID: 'V7au7L2Rw', // 劳动者用户角色ID
    },

    // 不同端的域名，暂时注释，这次合并不需要
    domainNames: {
      super: 'http://127.0.0.1:7003',
      operate: 'http://127.0.0.1:7005',
      jkqy: 'http://127.0.0.1:7001',
      service: 'http://127.0.0.1:7007',
      tj: 'http://127.0.0.1:6006',
    },

    // 是否开启以版本号(手动定版)控制的静态界面更新 默认为true  注意：手动更新优先级大于自动更新
    isVersionUpdate: true,
    // 版本号
    version: '1.0.561',
    // 是否开启静态界面自动更新 默认为false  警告：慎重！！！如果改为false，强烈建议手动清空缓存或开启手动定版并更新版本号后再加载，否则有可能加载的并非最新界面
    isAutoUpdate: false,

    // 解析PDF接口配置
    analysisPDFAPPCode: 'APPCODE 5303dc3ba4684cdc8141510434cd12cf',

    keys: 'frame',
    cluster: {
      listen: {
        port: 6006,
        hostname: '',
      },
    },
    diagnosisReportRouter: {
      match: [ctx => ctx.path.startsWith('/manage/diagnosisReport'), ctx => ctx.path.startsWith('/api/diagnosisReport')],
      match: [
        (ctx) => ctx.path.startsWith('/manage/diagnosisReport'),
        (ctx) => ctx.path.startsWith('/api/diagnosisReport'),
      ],
    },

    // 用户下载填写的excel模板http路径，代码拼接 /static + datatemplate_path
    data_template_path: '/dataTemplate',

    // cdn域名
    origin: 'http://localhost:6006',
    // 加密解密
    session_secret: 'duopu_secret',
    auth_cookie_name: 'duopu_jkqy_physicalExam',
    encrypt_key: '243d477d-d6bb-4ab1-b0a9-a1b1c9419562',
    cms_encrypt_key: 'duopu_jkqy',
    salt_aes_key: 'duopu_jkqy',
    salt_sha2_key: '7a58917b-d493-4940-87d4-ab2b0fa8a3b7',
    encryptApp_key: '751f621ea5c8f930',
    encryptApp_vi: '2624750004598718',
    // 密码有效期
    passwordExpiresIn: 90 * 24 * 60 * 60 * 1000,
    passwordValidityPeriod: true, // 是否启用密码有效期功能
    limitLoginAttemps: 3, // 登录尝试次数
    loginAttemptsTimeRange: 10 * 60 * 1000, // 登录连续尝试时间范围
    lockedLoginTime: 10 * 60 * 1000, // 登录锁定时间
    ifAutoUnlock: true, // 是否自动解锁

    // 安全性校验
    security: {
      csrf: {
        enable: false,
      },
    },
    // nunjucks模板引擎
    view: {
      defaultViewEngine: 'nunjucks',
      mapping: {
        '.html': 'nunjucks',
      },
    },

    proxy: true,
    ipHeaders: 'X-Real-IP, X-Forwarded-For',
    // 输出日志到终端
    logger: {
      disableConsoleAfterReady: false,
      dir: '/opt/log/tj/',
    },

    // 国际化
    i18n: {
      defaultLocale: 'zh-CN',
    },
    // // 百度地图 url和ak
    // bmap: {
    //   url: 'https://api.map.baidu.com/geocoding/v3/',
    //   ak: 'klt8PdaqU2RBUn3noMpzKtRw5xvMml9y',
    //   styleId: 'c5212ceaa7fe7147d983983febb8ecbd', // xxn的百度地图id
    // },
    // 百度地图 url和ak
    bmap: {
      url: 'https://api.map.baidu.com/geocoding/v3/',
      ak: 'k14GSWM76SjqfMZcdOWacMb3XmV9sdGl',
      styleId: 'bf656ab5c64b329624efdd27a48e953b',
    },
    gmap: {
      url: 'https://restapi.amap.com/v3/geocode/geo',
      key: 'c2755a184bf2bcbd83cb73aa0b501187',
      searchPlaceUrl: 'https://restapi.amap.com/v3/place/text', // 搜索POI
    },

    iServiceHost: 'http://127.0.0.1:8666',
    iService2Host: 'http://iservice2',
    // 是否开启数据库加密
    dbEncryption: process.env.DB_ENCRYPTION === 'true',
    // 是否允许福州国密接口调用
    ALLOW_FZGM: process.env.ALLOW_FZGM === 'true',

    platformName: process.env.platformName || '职业卫生体检信息报送平台', // 平台名称 - 主菜单左上角处的名称

    platformLogoShow: process.env.platformLogoShow === '0' ? false : true, // 平台主菜单左上角logo显示 - 环境变量配置时请用 0 设置隐藏
  };
};
