const Service = require('egg').Service;
const path = require('path');
const { nanoid } = require('nanoid');
const fs = require('fs');
const { mkdirp } = require('mkdirp');
const sendToWormhole = require('stream-wormhole');
const awaitWriteStream = require('await-stream-ready').write;
const moment = require('moment');
class ContractTableService extends Service {
  // 合同起草
  async addContract(query) {
    const { ctx } = this;
    query.createDate = new Date()
    await ctx.model.MedicalExamContract.create(query);
  }
  // 列表查询
  async getContractTableList(query) {
    const pageNum = Number(query.pageNum);
    const pageSize = Number(query.pageSize);
    const { ctx } = this;

    // 构建查询条件
    const filter = {};
    const match = {};
    if (query.cName) {
      match.cname = { $regex: query.cName, $options: 'i' };
    }
    if (query.auditStatu) {
      filter.auditStatu = query.auditStatu;
    }
    if (query.contractType) {
      filter.contractType = query.contractType;
    }
    if (query.contractPeopleNum) {
      filter.contractPeopleNum = query.contractPeopleNum;
    }
    // if (query.contractAmountMin) {
    //   filter.contractAmount = { $gte: Number(query.contractAmountMin) };
    // }
    // if (query.contractAmountMax) {
    //   filter.contractAmount = { $lte: Number(query.contractAmountMax) };
    // }
    if (query.contractAmountMin && !query.contractAmountMax) {
      filter.contractAmount = { $gte: Number(query.contractAmountMin) };
    }
    if (!query.contractAmountMin && query.contractAmountMax) {
      filter.contractAmount = { $lte: Number(query.contractAmountMin) };
    }
    if (query.contractAmountMin && query.contractAmountMax) {
      filter.contractAmount = { $gte: Number(query.contractAmountMin), $lte: Number(query.contractAmountMax) };
    }

    if (query.examStartDate) {
      filter.examStartDate = { $gte: query.examStartDate };
    }
    if (query.examEndDate) {
      filter.examEndDate = { $lte: query.examEndDate };
    }
    if (query.contractInitiator) {
      filter.contractInitiator = query.contractInitiator;
    }
    if (query.physicalOrgId) {
      filter.physicalOrgId = query.physicalOrgId;
    }
    // if (query.cName) {
    //   match.cname = { $regex: query.cName, $options: 'i' };
    // }
    if (query.companyContact) {
      filter.companyContact = { $regex: query.companyContact, $options: 'i' };
    }
    if (query.companyPhone) {
      filter.companyPhone = query.companyPhone;
    }
    if (query.unifiedCode) {
      filter.unifiedCode = { $regex: query.unifiedCode, $options: 'i' };
    }
    if (query.signDate) {
      filter.signDate = { $regex: query.signDate, $options: 'i' };
    }

    const res = await ctx.model.MedicalExamContract.find(filter)
      .populate({
        path: 'cId',
        select: 'cname code',
        match,
      })
      .populate({
        path: 'companyId',
        select: 'cname code',
      })
      .populate({
        path: 'physicalOrgId',
        select: 'name organization',
        // match,
      })
      .select('companyId agencyContact agencyPhone companyPhone companyContact contractAmount contractPeopleNum contractType contractSignDate contractDraftDate auditStatu examStartDate examEndDate contractFile')
      .skip((pageNum - 1) * pageSize)
      .limit(pageSize)
      .sort({ createDate: -1 });

    // 过滤掉未匹配联查条件的主表数据
    let result = res.filter(item => item.cId !== null);
    result = result.map(item => {
      const { cId, companyId, ...rest } = item.toObject(); // 拆分数据，将 cId 提取出来
      const { _id, id, ...cIdFields } = cId; // 从 cId 中移除 _id 和 id 字段
      if (query.contractInitiator === 0) { // 机构发起的
        cIdFields.phoneNum = companyId.phoneNum ? companyId.phoneNum.slice(0, 3) + '****' + companyId.phoneNum.slice(-4) : '';
        cIdFields.code = companyId.code ? companyId.code.slice(0, 5) + '********' + companyId.code.slice(-5) : '';
      } else { // 企业发起的
        cIdFields.phoneNum = cIdFields.phoneNum ? cIdFields.phoneNum.slice(0, 3) + '****' + cIdFields.phoneNum.slice(-4) : '';
        cIdFields.code = cIdFields.code ? cIdFields.code.slice(0, 5) + '********' + cIdFields.code.slice(-5) : '';
      }
      return {
        ...rest,
        ...cIdFields, // 平级合并剩余字段
      };
    });
    // 统计总数
    const sum = await await ctx.model.MedicalExamContract.find(filter).populate({
      path: 'cId',
      select: 'cname code contract phoneNum',
      match,
    });
    const total = sum.filter(item => item.cId !== null).length;

    return {
      list: result,
      pageNum,
      pageSize,
      total,
    };
  }

  // 体检机构选择用人单位时获得用人单位数据
  async getQyList(query) {
    const { ctx } = this;
    const searchCondition = {};
    if (query.cName) {
      searchCondition.cname = { $regex: query.cName, $options: 'i' };
    }
    const res = await ctx.model.Adminorg.find(searchCondition).select('cname code contract phoneNum');
    return res;
  }

  // 删除体检合同
  async deleteContract(query) {
    const { ctx } = this;
    await ctx.model.MedicalExamContract.findByIdAndDelete(query.id);
  }

  // 审核体检合同
  async auditContract(query) {
    const { ctx } = this;
    await ctx.model.MedicalExamContract.updateOne({ _id: query.id }, { $set: { auditStatu: query.auditStatu } });
    if (query.auditStatu === 2) {
      const info = await ctx.model.MedicalExamContract.find({ _id: query.id }).select('cId contractType');
      const type = info[0].contractType;
      const id = info[0].cId;
      const employees = await ctx.model.Employee.find({ EnterpriseID: id }).select('name phoneNum IDNum');
      const planData = employees.map(item => {
        return {
          idNumber: item.IDNum,
          name: item.name,
          contactPhone: item.phoneNum ? item.phoneNum : '',
          reservationStatu: 0,
          examType: type,
          contractId: query.id,
          refuseReason: '',
          EnterpriseId: id,
          employeeId: item._id,
        };
      });
      await ctx.model.MedicalExamPlan.create(planData);
    }
  }

  // 上传体检合同
  async uploadContract(query) {
    const { ctx } = this;
    const stream = await ctx.getFileStream();
    const uploadPath = path.join(ctx.app.config.upload_path, query.physicalOrgId);
    const extname = path.extname(stream.filename).toLowerCase(); // 文件扩展名称
    const fileName = path.basename(stream.filename, extname); // 文件名称
    const uploadFileName = fileName + `_${nanoid(10)}` + extname;
    if (!fs.existsSync(uploadPath)) {
      mkdirp.sync(uploadPath);
    }
    const target = path.join(uploadPath, `${uploadFileName}`);
    const writeStream = fs.createWriteStream(target);
    try {
      await awaitWriteStream(stream.pipe(writeStream));
    } catch (err) {
      // 必须将上传的文件流消费掉，要不然浏览器响应会卡死
      await sendToWormhole(stream);
      throw err;
    }
    return {
      uploadFileName,
      target,
    };
  }

  // 体检预约
  async medicalExamBook(query) {
    const { ctx } = this;
    query.reservationStatu = Number(query.reservationStatu);
    if (Array.isArray(query.id)) {
      await ctx.model.MedicalExamPlan.updateMany(
        { _id: { $in: query.id } },
        { $set: { reservationStatu: query.reservationStatu, refuseReason: query.refuseReason || '' } }
      );
      if (query.reservationStatu === 2) {
        query.id.forEach(async item => {
          await this.registerCheckNo(item);
        });
      }
    } else {
      if (query.reservationStatu === 2) {
        await this.registerCheckNo(query.id);
      }
      await ctx.model.MedicalExamPlan.updateOne(
        { _id: query.id },
        { $set: { reservationStatu: query.reservationStatu, refuseReason: query.refuseReason || '' } }
      );
    }
  }


  // 体检预约 加入事务
  // async medicalExamBook(query) {
  //   // 
  //   const { ctx } = this;
  //   const session = await ctx.model.startSession(); // 开启会话，用于事务处理
  //   session.startTransaction(); // 开启事务
  //   try { // 捕获错误，回滚操作
  //     query.reservationStatu = Number(query.reservationStatu);
  //     if (Array.isArray(query.id)) {
  //       await ctx.model.MedicalExamPlan.updateMany(
  //         { _id: { $in: query.id } },
  //         { $set: { reservationStatu: query.reservationStatu, refuseReason: query.refuseReason || '' } }
  //       );
  //       if (query.reservationStatu === 2) { // 预约成功，生成体检登记记录，并更新体检登记记录的体检号
  //         query.id.forEach(async item => { // 循环更新体检登记记录的体检号，并创建体检登记记录
  //           await this.registerCheckNo(item); // 调用registerCheckNo方法创建体检登记记录，并更新体检号
  //         }); // 结束循环，更新体检号和创建体检登记记录操作完成。
  //       } // 结束预约成功的操作，进行下一步操作。
  //     } else { // 单个预约操作，更新体检登记记录的体检号，并创建体检登记记录。
  //       await ctx.model.MedicalExamPlan.updateOne(
  //         { _id: query.id },
  //         { $set: { reservationStatu: query.reservationStatu, refuseReason: query.refuseReason || '' } }
  //       );
  //       if (query.reservationStatu === 2) { // 预约成功，生成体检登记记录，并更新体检登记记录的体检号。
  //         await this.registerCheckNo(query.id); // 调用registerCheckNo方法创建体检登记记录，并更新体检号。
  //       } // 结束预约成功的操作，进行下一步操作。
  //     } // 结束单个预约操作，进行下一步操作。
  //     await session.commitTransaction(); // 提交事务，完成预约操作。
  //   } catch (err) { // 捕获错误，回滚操作。
  //     await session.abortTransaction(); // 回滚操作，撤销预约操作。
  //     throw err; // 抛出错误，中断预约操作。
  //   }
  // }

  // 为体检创建体检登记记录
  // 先查找employeeId和contractId是否有相同的记录，如果有则更新，没有则创建。而不是直接创建
  async registerCheckNo(plan_id) {
    const { ctx } = this;
    const info = await ctx.model.MedicalExamPlan.findOne({ _id: plan_id })
      .populate('employeeId')
      .populate('contractId')
      .select('contractId EnterpriseId employeeId name idNumber contactPhone examType reservationDate');
    const { employeeId, EnterpriseId, contractId, name, idNumber, contactPhone, examType, reservationDate } = info
    const dataString = moment(reservationDate).format('YYYYMMDD')
    const checkNo = `${dataString}${info.employeeId._id.slice(0, 4)}${nanoid(4)}`
    await ctx.model.MedicalExamPlan.updateOne({ _id: plan_id }, { $set: { registerCheckNo: checkNo } });
    // 0离岗 1岗前 2在岗 => //String 1: 岗前, 2: 在岗, 3: 离岗,
    const registerExamType = { 0: '3', 1: '1', 2: '2' }
    // 部门
    const department = await this.findEmployeeDepartments(employeeId.departs);
    // console.log('🍊 department', department)
    // 工作场所
    const workspaces = await this.findEmployeeWorkspace({ EnterpriseID: employeeId.EnterpriseID, employeeId: employeeId._id });
    const workTypeName = this.getAllWorkspacesName(workspaces);
    // console.log('🍊 workTypeName', workTypeName)
    // 危害因素
    const hazardFactors = this.getAllWorkspacesHarms(workspaces);
    // console.log('🍊 hazardFactors', hazardFactors)

    const data = {
      checkNo,
      employeeID: employeeId._id,
      EnterpriseID: employeeId.EnterpriseID || EnterpriseId,
      contractID: contractId._id,
      physicalOrgID: ctx.session.physicalExamUserInfo.EnterpriseID,
      examPlanID: plan_id,
      examType: registerExamType[examType],
      // checkType: contractId.checkType // 1: 职业健康体检, 2: 一般体检
      checkType: 1,

      name,
      phone: contactPhone,
      department,
      workType: workTypeName,
      hazardFactors

    }
    if (employeeId.gender) {
      data.gender = (+employeeId.gender + 1) + ''
    }
    if (employeeId.marriage) {
      data.maritalStatus = employeeId.marriage === '已婚' ? '20' : '10'
    }
    if (idNumber) {
      data.idNumber = idNumber
      if (idNumber.length === 18) { // 18位身份证
        const birthDateStr = idNumber.slice(6, 14);
        const birthDate = moment(birthDateStr, 'YYYYMMDD', true); // 严格模式
        if (birthDate.isValid()) {
          data.birthDate = birthDate.toDate();
        }
        // 如果没有性别，则根据第17位判断，奇数为男偶数为女
        if (!data.gender) {
          data.gender = idNumber[16] % 2 === 0 ? '2' : '1';
        }
      }
    }
    const find = await ctx.model.HealthCheckRegister.findOne({ employeeID: employeeId._id, contractID: contractId._id })
    if (find) {
      return await ctx.model.HealthCheckRegister.updateOne({ _id: find._id }, { $set: data })
    } else {
      return await ctx.model.HealthCheckRegister.create(data)
    }
  }

  // 体检预约个人详情
  async getPersonDetail(query) {
    const { ctx } = this;
    const pageNum = Number(query.pageNum);
    const pageSize = Number(query.pageSize);
    const filter = {};
    if (query.contractId) {
      filter.contractId = query.contractId;
    }
    if (query.reservationStatu) {
      filter.reservationStatu = query.reservationStatu;
    }
    if (query.examType) {
      filter.examType = query.examType;
    }
    if (query.name) {
      filter.name = { $regex: query.name, $options: 'i' };
    }
    if (query.contactPhone) {
      filter.contactPhone = { $regex: query.contactPhone, $options: 'i' };
    }
    if (query.idNumber) {
      filter.idNumber = { $regex: query.idNumber, $options: 'i' };
    }
    filter.reservationStatu = { $ne: 0 };
    const res = await ctx.model.MedicalExamPlan.find(filter)
      .select('name contactPhone idNumber contractType reservationStatu refuseReason reservationDate')
      .skip((pageNum - 1) * pageSize)
      .limit(pageSize);

    // 统计总数
    const sum = await await ctx.model.MedicalExamPlan.find(filter).select('contractId idNumber name')
    const total = sum.filter(item => item.contractId !== null).length;

    return {
      res,
      total
    }
  }

  // 获取体检机构详情
  async getPhysicalDetail(query) {
    try {
      const { ctx } = this;
      const searchCondition = {};
      if (query.id) {
        searchCondition._id = { _id: query.id };
      }
      const res = await ctx.model.PhysicalExamOrg.findOne(searchCondition).select('name organization contract phoneNum');
      return res
    } catch (error) {
      // 错误捕获
    }
  }

  // 编辑合同实收金额
  async editReceivedAmount(query) {
    try {
      const { ctx } = this;
      const { receivedAmount } = query
      const res = await ctx.model.MedicalExamContract.updateOne({ _id: query._id }, { $set: { receivedAmount } });
      ctx.auditLog('修改合同实收金额成功', `合同_id:${query._id}`, 'info');
      return res
    }
    catch {
      ctx.auditLog('修改合同实收金额失败', `合同_id:${query._id}`, 'error');
    }
  }

  // 获取员工部门
  // departs 是一个二维数组，表示员工所属的多个部门，第二维数组中的每个元素都是Dingtree的_id
  async findEmployeeDepartments(departs) {
    let res = '';
    const { ctx } = this;
    if (!Array.isArray(departs) || departs.length === 0) {
      return res;
    }
    for (let i = 0; i < departs.length; i++) {
      let name = ''
      const item = departs[i];

      if (!item || !Array.isArray(item) || item.length === 0) {
        continue; // 如果当前项不是数组或为空，则跳过
      }

      for (let index = 0; index < item.length; index++) {
        const element = item[index];
        const depart = await ctx.model.Dingtree.findOne({ _id: element });
        if (depart) {
          name += name.length ? '-' + depart.name : depart.name;
        }
      }
      res += res.length ? ',' + name : name;
    }
    return res;
  }


  // 获取员工当前工作场所
  async findEmployeeWorkspace({ EnterpriseID, employeeId }) {
    const { ctx } = this;
    if (!employeeId) {
      throw new Error('employeeId is required');
    }
    const workspaces = EnterpriseID
      ? await ctx.model.Workspace.find({ EnterpriseID, employees: { $elemMatch: { employeeId } } })
      : await ctx.model.Workspace.find({ employees: { $elemMatch: { employeeId } } });
    return workspaces;
  }

  getAllWorkspacesName(workspaces) {
    let res = '';
    if (!Array.isArray(workspaces) || workspaces.length === 0) {
      return res;
    }
    for (let i = 0; i < workspaces.length; i++) {
      const workspace = workspaces[i];
      let temp = '';
      if (workspace.workshopName) {
        temp += workspace.workshopName + '-';
      }
      temp += workspace.workspaceName + '-' + workspace.workTypeName;
      res += res.length ? ',' + temp : temp;
    }
    return res;
  }

  getAllWorkspacesHarms(workspaces) {
    if (!Array.isArray(workspaces) || workspaces.length === 0) {
      return [];
    }
    const harm = new Set(); // 使用Set去重
    for (let i = 0; i < workspaces.length; i++) {
      const workspace = workspaces[i];
      for (let index = 0; index < workspace.stations.length; index++) {
        const station = workspace.stations[index];
        if (station.harmFactors && station.harmFactors.length > 0) {
          station.harmFactors.forEach(item => harm.add(item));
        }
      }
    }
    return Array.from(harm);
  }
}

module.exports = ContractTableService;