<template>
    <el-dialog top="3%" width="80%" title="批量导入健康检查项目" :close-on-click-modal="false" :visible.sync="currentIsShow">
        <div class="batch-import-container">
            <!-- 导入步骤 -->
            <el-steps :active="currentStep" finish-status="success" align-center>
                <el-step title="上传文件" description="选择Excel文件"></el-step>
                <el-step title="数据预览" description="预览和编辑数据"></el-step>
                <el-step title="导入完成" description="确认导入"></el-step>
            </el-steps>

            <!-- 步骤1: 文件上传 -->
            <div v-if="currentStep === 0" class="upload-step">
                <div class="upload-tips">
                    <h3>导入说明：</h3>
                    <ul>
                        <li>请下载模板文件，按照模板格式填写数据</li>
                        <li>支持.xlsx、.xls格式的Excel文件</li>
                        <li>项目名称和项目编号为必填项</li>
                        <li>数值类型字段请填写数字，选择类型字段请按照说明填写</li>
                    </ul>
                </div>
                
                <UploadExcel 
                    :on-success="handleExcelSuccess" 
                    @downloadTemplate="downloadTemplate"
                    class="upload-excel-component"
                />
            </div>

            <!-- 步骤2: 数据预览 -->
            <div v-if="currentStep === 1" class="preview-step">
                <div class="preview-header">
                    <h3>数据预览 (共 {{ tableData.length }} 条)</h3>
                    <div class="preview-actions">
                        <el-button @click="prevStep">上一步</el-button>
                        <el-button type="primary" @click="nextStep" :disabled="tableData.length === 0">下一步</el-button>
                    </div>
                </div>
                
                <el-table :data="tableData" border stripe max-height="400" style="width: 100%">
                    <el-table-column prop="projectName" label="项目名称" width="120" show-overflow-tooltip />
                    <el-table-column prop="projectNumber" label="项目编号" width="120" show-overflow-tooltip />
                    <el-table-column prop="resultType" label="结果类型" width="100" />
                    <el-table-column prop="msrunt" label="计量单位" width="100" />
                    <el-table-column prop="genderLimit" label="性别限制" width="100" />
                    <el-table-column prop="marriage" label="婚姻限制" width="100" />
                    <el-table-column prop="suitType" label="适用类型" width="150" show-overflow-tooltip />
                    <el-table-column prop="isLimitAge" label="年龄限制" width="100" />
                    <el-table-column prop="ageRange" label="年龄范围" width="120" show-overflow-tooltip />
                    <el-table-column prop="isStandard" label="参考范围" width="100" />
                    <el-table-column prop="standardRange" label="参考值" width="120" show-overflow-tooltip />
                    <el-table-column label="状态" width="80">
                        <template slot-scope="scope">
                            <el-tag :type="scope.row.status === 'success' ? 'success' : 'danger'" size="small">
                                {{ scope.row.status === 'success' ? '正常' : '错误' }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="错误信息" min-width="150" show-overflow-tooltip>
                        <template slot-scope="scope">
                            <span style="color: red;">{{ scope.row.errorMsg }}</span>
                        </template>
                    </el-table-column>
                </el-table>
            </div>

            <!-- 步骤3: 导入确认 -->
            <div v-if="currentStep === 2" class="confirm-step">
                <div class="confirm-header">
                    <h3>导入确认</h3>
                </div>
                
                <div class="import-summary">
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <div class="summary-item">
                                <div class="summary-number">{{ tableData.length }}</div>
                                <div class="summary-label">总数据量</div>
                            </div>
                        </el-col>
                        <el-col :span="8">
                            <div class="summary-item success">
                                <div class="summary-number">{{ validDataCount }}</div>
                                <div class="summary-label">有效数据</div>
                            </div>
                        </el-col>
                        <el-col :span="8">
                            <div class="summary-item error">
                                <div class="summary-number">{{ errorDataCount }}</div>
                                <div class="summary-label">错误数据</div>
                            </div>
                        </el-col>
                    </el-row>
                </div>

                <div class="confirm-actions">
                    <el-button @click="prevStep">上一步</el-button>
                    <el-button type="primary" @click="confirmImport" :disabled="validDataCount === 0">
                        确认导入 {{ validDataCount }} 条数据
                    </el-button>
                </div>
            </div>
        </div>

        <div slot="footer" class="dialog-footer" v-if="currentStep === 0">
            <el-button @click="close">取消</el-button>
        </div>
    </el-dialog>
</template>

<script>
import UploadExcel from './UploadExcel/index.vue'
import * as XLSX from 'xlsx'
import { downloadHealthCheckTemplate, validateExcelData } from '@/utils/excelTemplate'

export default {
    name: 'BatchImport',
    components: {
        UploadExcel
    },
    props: {
        show: {
            type: Boolean,
            default: false
        },
        resultTypeOptions: {
            type: Array,
            default: () => []
        },
        unitOption: {
            type: Array,
            default: () => []
        }
    },
    computed: {
        currentIsShow: {
            get() {
                return this.show
            },
            set(val) {
                this.$emit('update:show', val)
            }
        },
        validDataCount() {
            return this.tableData.filter(item => item.status === 'success').length
        },
        errorDataCount() {
            return this.tableData.filter(item => item.status === 'error').length
        }
    },
    data() {
        return {
            currentStep: 0,
            tableData: [],
            excelData: null
        }
    },
    watch: {
        show(val) {
            if (val) {
                this.resetData()
            }
        }
    },
    methods: {
        resetData() {
            this.currentStep = 0
            this.tableData = []
            this.excelData = null
        },
        nextStep() {
            if (this.currentStep < 2) {
                this.currentStep++
            }
        },
        prevStep() {
            if (this.currentStep > 0) {
                this.currentStep--
            }
        },
        close() {
            this.currentIsShow = false
            this.resetData()
        },
        // 下载模板
        downloadTemplate() {
            downloadHealthCheckTemplate()
        },
        // 处理Excel上传成功
        handleExcelSuccess(data) {
            // 验证Excel数据格式
            const validation = validateExcelData(data.results)
            if (!validation.isValid) {
                this.$message.error('Excel格式错误：' + validation.errors.join('；'))
                return
            }

            this.excelData = data
            this.parseExcelData(data.results)
            this.nextStep()
        },
        // 解析Excel数据
        parseExcelData(results) {
            this.tableData = results.map((row, index) => {
                const item = this.convertRowToItem(row)
                const validation = this.validateItem(item)

                return {
                    ...item,
                    status: validation.isValid ? 'success' : 'error',
                    errorMsg: validation.errors.join('; ')
                }
            }).filter(item => item.projectName || item.projectNumber) // 过滤空行
        },
        // 将Excel行数据转换为项目数据
        convertRowToItem(row) {
            const item = {
                projectName: row['项目名称'] || '',
                projectNumber: row['项目编号'] || '',
                resultType: this.convertResultType(row['结果类型']),
                msrunt: this.convertUnit(row['计量单位']),
                genderLimit: this.convertGenderLimit(row['性别限制']),
                marriage: this.convertMarriage(row['婚姻限制']),
                suitType: this.convertSuitType(row['适用类型']),
                isLimitAge: this.convertYesNo(row['是否限制年龄']),
                ageLimitMin: this.convertNumber(row['最小年龄']),
                ageLimitMax: this.convertNumber(row['最大年龄']),
                isStandard: this.convertYesNo(row['是否有参考范围']),
                standardValueMin: this.convertNumber(row['参考值下限']),
                standardValueMax: this.convertNumber(row['参考值上限']),
                isExtremeValue: this.convertYesNo(row['是否有极值范围']),
                extremeValueMin: this.convertNumber(row['最小极值']),
                extremeValueMax: this.convertNumber(row['最大极值']),
                highValueTips: row['偏高提示'] || '',
                lowValueTips: row['偏低提示'] || ''
            }

            // 添加显示字段
            item.ageRange = item.isLimitAge === 1 && item.ageLimitMin && item.ageLimitMax
                ? `${item.ageLimitMin}-${item.ageLimitMax}岁` : '不限'
            item.standardRange = item.isStandard === 1 && item.standardValueMin && item.standardValueMax
                ? `${item.standardValueMin}-${item.standardValueMax}` : '无'

            return item
        },
        // 转换结果类型
        convertResultType(value) {
            if (!value) return ''
            const qualitativeOption = this.resultTypeOptions.find(opt => opt.name === '定性')
            const quantitativeOption = this.resultTypeOptions.find(opt => opt.name === '定量')
            const typeMap = {
                '定性': qualitativeOption ? qualitativeOption.value : '',
                '定量': quantitativeOption ? quantitativeOption.value : ''
            }
            return typeMap[value] || value
        },
        // 转换单位
        convertUnit(value) {
            if (!value) return ''
            const unit = this.unitOption.find(opt => opt.name === value)
            return unit ? unit.value : value
        },
        // 转换性别限制
        convertGenderLimit(value) {
            if (!value) return 1
            const genderMap = {
                '不限': 1,
                '男': 2,
                '男性': 2,
                '女': 3,
                '女性': 3
            }
            return genderMap[value] || 1
        },
        // 转换婚姻限制
        convertMarriage(value) {
            if (!value) return 1
            const marriageMap = {
                '不限': 1,
                '未婚': 2,
                '已婚': 3
            }
            return marriageMap[value] || 1
        },
        // 转换适用类型
        convertSuitType(value) {
            if (!value) return []
            const types = []
            if (value.includes('职业健康检查项目')) types.push(1)
            if (value.includes('平时体检项目') || value.includes('平时检查项目')) types.push(2)
            return types.length > 0 ? types : [1] // 默认为职业健康检查项目
        },
        // 转换是否类型
        convertYesNo(value) {
            if (!value) return 2
            const yesNoMap = {
                '是': 1,
                '否': 2,
                'true': 1,
                'false': 2,
                '1': 1,
                '0': 2
            }
            return yesNoMap[String(value)] || 2
        },
        // 转换数字
        convertNumber(value) {
            if (!value || value === '') return null
            const num = Number(value)
            return isNaN(num) ? null : num
        },
        // 验证数据项
        validateItem(item) {
            const errors = []

            // 必填项验证
            if (!item.projectName) {
                errors.push('项目名称不能为空')
            }
            if (!item.projectNumber) {
                errors.push('项目编号不能为空')
            }

            // 年龄限制验证
            if (item.isLimitAge === 1) {
                if (!item.ageLimitMin || !item.ageLimitMax) {
                    errors.push('启用年龄限制时，最小年龄和最大年龄不能为空')
                } else if (item.ageLimitMin >= item.ageLimitMax) {
                    errors.push('最小年龄必须小于最大年龄')
                }
            }

            // 参考范围验证
            if (item.isStandard === 1) {
                if (item.standardValueMin === null || item.standardValueMax === null) {
                    errors.push('启用参考范围时，参考值下限和上限不能为空')
                } else if (item.standardValueMin >= item.standardValueMax) {
                    errors.push('参考值下限必须小于上限')
                }
            }

            // 极值范围验证
            if (item.isExtremeValue === 1) {
                if (item.extremeValueMin === null || item.extremeValueMax === null) {
                    errors.push('启用极值范围时，最小极值和最大极值不能为空')
                } else if (item.extremeValueMin >= item.extremeValueMax) {
                    errors.push('最小极值必须小于最大极值')
                }
            }

            // 适用类型验证
            if (!item.suitType || item.suitType.length === 0) {
                errors.push('适用类型不能为空')
            }

            return {
                isValid: errors.length === 0,
                errors
            }
        },
        // 确认导入
        confirmImport() {
            const validData = this.tableData.filter(item => item.status === 'success')

            // 清理显示字段，只保留需要的数据
            const importData = validData.map(item => {
                const cleanItem = { ...item }
                delete cleanItem.status
                delete cleanItem.errorMsg
                delete cleanItem.ageRange
                delete cleanItem.standardRange
                return cleanItem
            })

            this.$emit('confirm', importData)
            this.close()
        }
    }
}
</script>

<style scoped>
.batch-import-container {
    padding: 20px;
}

.upload-step {
    margin-top: 30px;
}

.upload-tips {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 4px;
    margin-bottom: 20px;
}

.upload-tips h3 {
    margin-top: 0;
    color: #409eff;
}

.upload-tips ul {
    margin: 10px 0;
    padding-left: 20px;
}

.upload-tips li {
    margin: 5px 0;
    color: #666;
}

.preview-step {
    margin-top: 30px;
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.confirm-step {
    margin-top: 30px;
}

.import-summary {
    margin: 30px 0;
}

.summary-item {
    text-align: center;
    padding: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
}

.summary-item.success {
    border-color: #67c23a;
    background: #f0f9ff;
}

.summary-item.error {
    border-color: #f56c6c;
    background: #fef0f0;
}

.summary-number {
    font-size: 32px;
    font-weight: bold;
    color: #409eff;
}

.summary-item.success .summary-number {
    color: #67c23a;
}

.summary-item.error .summary-number {
    color: #f56c6c;
}

.summary-label {
    margin-top: 10px;
    color: #666;
}

.confirm-actions {
    text-align: center;
    margin-top: 30px;
}
</style>
