// getFeesList
/*
 * @Author: mj
 * @Description: 职业健康检查
 */

const HealthCheckFeeManageController = {
    // 获取合同非费用信息
    async getFeesList(ctx) {
        try {
            const query = ctx.request.body
            query.physicalOrgId = ctx.session.physicalExamUserInfo.EnterpriseID;
            const result = await ctx.service.healthCheckFeeManage.getFeesList(query)
            ctx.helper.renderSuccess(ctx, {
                data: result,
                message: '获取费用列表成功'
            })
        } catch (err) {
            ctx.helper.renderFail(ctx, {
                message: err
            })
        }
    },

    // 根据合同ID获取已登记人员列表
    async getPersonListByContractId(ctx) {
        try {
            const query = ctx.request.query
            const result = await ctx.service.healthCheckFeeManage.getPersonListByContractId(query)
            ctx.helper.renderSuccess(ctx, {
                data: result,
                message: '获取体检人员列表成功'
            })
        } catch (err) {
            ctx.helper.renderFail(ctx, {
                message: err
            })
        }
    },

    // 根据合同ID获取费用明细列表
    async getFeeDetailByContractId(ctx) {
        try {
            const query = ctx.request.query
            const result = await ctx.service.healthCheckFeeManage.getFeeDetailByContractId(query)
            ctx.helper.renderSuccess(ctx, {
                data: result,
                message: '获取费用明细成功'
            })
        } catch (err) {
            ctx.helper.renderFail(ctx, {
                message: err
            })
        }
    },

    // 获取合同附件
    async getContractFile(ctx) {
        try {
            const query = ctx.request.query
            const result = await ctx.service.healthCheckFeeManage.getContractFile(query)
            ctx.helper.renderSuccess(ctx, {
                data: result,
                message: '获取合同附件成功'
            })
        } catch (err) {
            ctx.helper.renderFail(ctx, {
                message: err
            })
        }
    }
}

module.exports = HealthCheckFeeManageController; 