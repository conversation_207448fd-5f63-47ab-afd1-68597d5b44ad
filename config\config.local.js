const path = require("path");
// const isDocker = process.env.BUILD_ENV === 'docker';
// const mongohost = isDocker ? 'mongodb' : '127.0.0.1:27017';
// const mongobin = isDocker ? '' : '/Users/<USER>/Documents/frame/softs/mongodb/bin/';

module.exports = (appInfo) => {
  return {
    admin_root_path: "http://localhost",
    // DEV_CONFIG_MODULES_BEGIN
    dev_modules: [
      // "navbar", // 导航栏
      // 'dashboard', // 主页
      // 'clientInfo', // 委托单位列表
      // 'physicalCompany', // 基础信息
      // 'physicalExaminationProject', // 体检项目 废弃
      // 'diagnosisReport', // 诊断信息上报
      // 'oneInput', // 报告一键导入
      // 'healthcheck', // 体检项目
      // 'operateLog', // 操作日志
      // 'interfaceLog', // 体检接口日志
      // 'regulations', // 法律法规
      // 'employee', // 员工列表
      // 'contractTable', // 体检合同列表
      // 'checkItemsManage', // 检查项目管理
      // 'physicalExaminationAppointment', // 体检预约管理
      // "checkOrgRecord", // 体检机构备案
      // 'suspectDiseaseReport',     // 疑似职业病上报
      // 'tjHealthCheckWarning',        // 体检端职业健康检查管理预警
      // 'tjHealthCheckStatistic',       // 体检端职业健康检查统计
      // 'tjHealthCheckReportManage',     // 体检端体检报告管理
      // 'healthCheckReservationDealWith', // 职业健康检查预约处理

      // 'hcProgressManagement',        // 职业健康检查过程管理
      // "healthCheckFeeManages",          // 职业健康检查费用管理
      // 'healthCheckFeeManage',           // 收费标准设置
      'healthCheckSetting', // 职业健康检查设置 检查项目设置
      // 'hcCompanyManage',                // 企业维护(机构段/检查设置)
      // 'hcDepartmentManage',             // 体检科室维护
      // 'hcInsitutionManage',             // 体检机构设置
      // 'computedItems',                  // 计算类项目
      // 'hcReservationTimeSetting',       // 预约时间设置
      // 'reservationNumberSetting',       // 预约号设置
      // 'hcAutoJudgeManagement',          // 自动判定维护

    ],
    // DEV_CONFIG_MODULES_END
    mongoose: {
      client: {
        // url: 'mongodb://dbadmin:<EMAIL>:25000/frameData?authSource=admin',
        url: 'mongodb://dbadmin:<EMAIL>:25000/zyws-xjbt0414?authSource=admin',
        // url: '****************************************************************************',
        // url: 'mongodb://127.0.0.1:27017/zyws-xjbt0409',
        options: {
          useCreateIndex: true,
          useUnifiedTopology: true,
          keepAlive: 3000,
        },
      },
    },
    // mongodb相关路径
    mongodb: {
      binPath: "",
      backUpPath: path.join(appInfo.baseDir, "databak/"),
    },
    static: {
      prefix: "/static",
      dir: [
        path.join(appInfo.baseDir, "app/public"),
        path.join(appInfo.baseDir, "backstage/dist"),
        // 'E:/jkqy/app/public',
        // 'E:/jkqyOperate/app/public',
      ],
      maxAge: 31536000,
    },
    logger: {
      dir: path.join(appInfo.baseDir, "logs"),
    },
    // 是否开启以版本号(手动定版)控制的静态界面更新 默认为true 注意：手动更新优先级大于自动更新
    isVersionUpdate: false,
    // 版本号
    // 是否开启静态界面自动更新 默认为false  警告：慎重！！！如果改为false，强烈建议手动清空缓存或开启手动定版并更新版本号后再加载，否则有可能加载的并非最新界面
    isAutoUpdate: true,

    server_path: "http://127.0.0.1:7008",
    server_api: "http://127.0.0.1:7008/api",

    // iServiceHost: 'http://iservice.beasts.wang',
    iServiceHost: 'https://iservice.xixids.com',
    // iServiceHost: 'http://127.0.0.1:8666',
    // server_path: 'http://jkqy.hgfvbv.xyz',
    // server_api: 'http://jkqy.hgfvbv.xyz/api',

    // 首页默认展示区域
    dashboard_area_code: "", // 没有的话默认是全国
    dbEncryption: false,

    platformName:
      '<span style="font-size:12px">新疆生产建设兵团<br/>职业健康管理信息化系统</span>', // 平台名称 - 主菜单左上角处的名称
    platformLogoShow: false, // 平台logo显示 - 主菜单左上角名称前

    branch: "xjbt", // 测试用, 可以删
    // 福州国密url
    fzgmBaseUrl: process.env.FZGM_BASE_URL || "http://*************",
    // 福州国密业务key
    fzgmKey: process.env.FZGM_KEY || "n6beoq8b9g7te7e5",
    // 签名算法
    hmacSignAlgorithm: process.env.HMAC_SIGN_ALGORITHM || "FZsm3",
    // 密码加密算法
    passwordEncryptionAlgorithm:
      process.env.PASSWORD_ENCRYPTION_ALGORITHM || "FZsm3",
    // 是否开启数据库加密
    dbEncryption: process.env.DB_ENCRYPTION === 'true',
    // dbEncryption:false,
    // 是否允许福州国密接口调用
    ALLOW_FZGM: process.env.ALLOW_FZGM === "true",
    tjOidc: {
      // 门户oidc认证系统
      oidcInfo: {
        // api_host: "http://127.0.0.1:7002",
        api_host:'https://xjbtportal.jkqy.cn',
        realm_name: "",
        client_id: "pLO-g-ey36L2rX2OGdkgh",
        client_secret:
          "N3FeoO5BkmFTBMKgLOK7f",
        scope: "read",
        grant_type: "authorization_code",
        redirect_uri: "http://127.0.0.1:7008/xjbtcallback",
      },
    },
  };
};
