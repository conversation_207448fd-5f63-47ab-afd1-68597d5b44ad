# 健康检查项目批量导入接口设计文档

## 接口概述

### 接口名称
批量导入健康检查项目

### 接口路径
`POST /manage/healthCheckTask/batchImportHealthItems`

### 功能描述
支持通过Excel文件批量导入健康检查项目数据，包括数据验证、格式转换和批量保存功能。

## 请求参数

### 请求体格式
```json
{
  "items": [
    {
      "projectName": "血常规",
      "projectNumber": "XCG001",
      "resultType": "定量类型的value值",
      "msrunt": "单位的value值",
      "genderLimit": 1,
      "marriage": 1,
      "suitType": [1, 2],
      "isLimitAge": 1,
      "ageLimitMin": 18,
      "ageLimitMax": 65,
      "isStandard": 1,
      "standardValueMin": 120,
      "standardValueMax": 160,
      "isExtremeValue": 2,
      "extremeValueMin": null,
      "extremeValueMax": null,
      "highValueTips": "血红蛋白偏高",
      "lowValueTips": "血红蛋白偏低"
    }
  ]
}
```

### 字段说明

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| projectName | String | 是 | 检查项目名称 |
| projectNumber | String | 是 | 检查项目编号 |
| resultType | String | 否 | 结果类型，对应resultTypeOptions中的value值 |
| msrunt | String | 否 | 计量单位，对应unitOption中的value值 |
| genderLimit | Number | 否 | 性别限制：1-不限，2-男，3-女 |
| marriage | Number | 否 | 婚姻限制：1-不限，2-未婚，3-已婚 |
| suitType | Array | 否 | 适用类型：[1]-职业健康检查项目，[2]-平时体检项目，[1,2]-两者都适用 |
| isLimitAge | Number | 否 | 是否限制年龄：1-是，2-否 |
| ageLimitMin | Number | 否 | 最小年龄限制，当isLimitAge=1时必填 |
| ageLimitMax | Number | 否 | 最大年龄限制，当isLimitAge=1时必填 |
| isStandard | Number | 否 | 是否有参考范围：1-是，2-否 |
| standardValueMin | Number | 否 | 参考值下限，当isStandard=1时必填 |
| standardValueMax | Number | 否 | 参考值上限，当isStandard=1时必填 |
| isExtremeValue | Number | 否 | 是否有极值范围：1-是，2-否 |
| extremeValueMin | Number | 否 | 最小极值，当isExtremeValue=1时必填 |
| extremeValueMax | Number | 否 | 最大极值，当isExtremeValue=1时必填 |
| highValueTips | String | 否 | 偏高提示 |
| lowValueTips | String | 否 | 偏低提示 |

## 响应格式

### 成功响应
```json
{
  "status": 200,
  "message": "批量导入成功",
  "data": {
    "successCount": 10,
    "failCount": 0,
    "totalCount": 10,
    "failedItems": []
  }
}
```

### 失败响应
```json
{
  "status": 400,
  "message": "批量导入失败",
  "data": {
    "successCount": 8,
    "failCount": 2,
    "totalCount": 10,
    "failedItems": [
      {
        "index": 3,
        "projectName": "项目名称",
        "error": "项目编号已存在"
      },
      {
        "index": 7,
        "projectName": "项目名称2",
        "error": "参考值范围无效"
      }
    ]
  }
}
```

## 数据验证规则

### 必填字段验证
- projectName: 不能为空
- projectNumber: 不能为空且不能重复

### 数值范围验证
- 年龄限制：当isLimitAge=1时，ageLimitMin < ageLimitMax
- 参考范围：当isStandard=1时，standardValueMin < standardValueMax
- 极值范围：当isExtremeValue=1时，extremeValueMin < extremeValueMax

### 枚举值验证
- genderLimit: 1, 2, 3
- marriage: 1, 2, 3
- suitType: 数组，元素只能是1或2
- isLimitAge: 1, 2
- isStandard: 1, 2
- isExtremeValue: 1, 2

## 业务逻辑

### 数据处理流程
1. 接收前端传来的items数组
2. 逐条验证数据格式和业务规则
3. 检查项目编号是否重复（包括数据库中已存在的）
4. 批量插入有效数据到数据库
5. 返回处理结果统计

### 错误处理
- 单条数据验证失败不影响其他数据的导入
- 记录失败的数据项和具体错误信息
- 返回详细的成功/失败统计

### 数据库操作
- 使用事务确保数据一致性
- 设置physicalOrgId字段（从当前登录用户获取）
- 设置useStatus为1（正常状态）
- 自动生成_id和时间戳字段

## 前端数据转换说明

前端已实现Excel数据到API数据格式的转换，包括：

1. **文本到枚举值转换**：
   - "不限/男/女" → 1/2/3
   - "是/否" → 1/2
   - "职业健康检查项目/平时体检项目" → [1]/[2]/[1,2]

2. **数据类型转换**：
   - 字符串数字 → Number类型
   - 空值处理 → null

3. **数据验证**：
   - 必填字段检查
   - 数值范围验证
   - 逻辑关系验证

## 测试用例

### 正常数据示例
参考Excel模板中的示例数据

### 异常数据示例
- 缺少必填字段
- 数值范围错误
- 枚举值无效
- 项目编号重复

## 注意事项

1. 需要根据当前登录用户设置physicalOrgId
2. 项目编号需要在当前机构范围内唯一
3. 建议设置单次导入数量限制（如1000条）
4. 考虑大数据量导入的性能优化
5. 记录操作日志用于审计
