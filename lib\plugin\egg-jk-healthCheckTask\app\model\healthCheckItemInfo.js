// 检查项目
module.exports = app => {
    const mongoose = app.mongoose;
    const shortid = require('shortid');
    const Schema = mongoose.Schema;

    const healthCheckItemSchema = new Schema(
        {
            _id: {
                type: String,
                default: shortid.generate,
            },
            projectName: {     // 检查项目名称
                type: String
            },
            projectNumber: {    // 检查项目编号
                type: String
            },
            marriage: {         // 是否限制婚姻
                type: Number // 1不限 2未婚 3已婚
            },
            genderLimit: {      // 是否限制性别
                type: Number // 1 不限 2男 3女
            },
            isLimitAge: {      // 是否限制年龄
                type: Number // 1是 2否
            },
            ageLimitMin: {    // 年龄最小值
                type: Number
            },
            ageLimitMax: {    // 年龄最大值
                type: Number
            },
            isStandard:{    // 是否有参考范围
                type:Number // 1是 2否
            },
            standardValueMin: {
                type: Number      // 参考值下限
            },
            standardValueMax: {    // 参考值上限
                type: Number
            },
            // 偏高提示
            highValueTips: {
                type: String,
            },
            // 偏低提示
            lowValueTips: {
                type: String,
            },
            msrunt: {      // // 计量单位
                type: String,
            },
            resultType: {
                type: String
            },
            suitType: {     // // 适用类型
                type:Array  // 1职业健康检查项目 2平时体检项目
            },
            isExtremeValue:{ // 是否有极值范围
                type:Number     // 1是 2否
            },
            extremeValueMin:{ // 最小极值
                type:Number
            },
            extremeValueMax:{ // 最大极值
                type:Number
            },
            useStatus:{
                type:Number,     // 标识项目删除状态，1代表正常使用，2代表已删除
                default:1
            },
            // jdgptn: {   // jdgptn	判断模式 1：定性2：定量 // 判断模式
            //     type: Number,
            // },
            physicalOrgId: { // 体检机构id
                type: String,
                ref: 'PhysicalExamOrg',
            }
        },
        {
            timestamps: true,
        }
    );
    return mongoose.model('HealthCheckItemInfo', healthCheckItemSchema, 'HealthCheckItemInfos');
};
