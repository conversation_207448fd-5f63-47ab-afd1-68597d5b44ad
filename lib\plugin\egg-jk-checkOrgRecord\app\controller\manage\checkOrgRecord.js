const fs = require("fs");
const awaitWriteStream = require("await-stream-ready").write;
const path = require("path");
const sendToWormhole = require("stream-wormhole");
const checkOrgRecordController = {
  async addRecord(ctx) {
    try {
      const res = await ctx.service.checkOrgRecord.addRecord(ctx.request.body);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: "成功",
        status: 200,
      });
    } catch (error) {
      console.log(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },

  async deleteRecord(ctx) {
    try {
      const res = await ctx.service.checkOrgRecord.deleteRecord(
        ctx.request.body
      );
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: "成功",
        status: 200,
      });
    } catch (error) {
      console.log(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },

  async updateRecord(ctx) {
    try {
      const { data, id } = ctx.request.body;
      const businessLicense = [],
        practicingLicense = [],
        diagnosisLicense = [],
        qualityControl = [],
        checkReport = [],
        recordTable = [];
      data.forEach((item) => {
        if (item.type === "businessLicense") {
          businessLicense.push(item);
        }
        if (item.type === "practicingLicense") {
          practicingLicense.push(item);
        }
        if (item.type === "diagnosisLicense") {
          diagnosisLicense.push(item);
        }

        if (item.type === "qualityControl") {
          qualityControl.push(item);
        }
        if (item.type === "checkReport") {
          checkReport.push(item);
        }
        if (item.type === "recordTable") {
          recordTable.push(item);
        }
      });

      let res;

      if (businessLicense.length > 0) {
        res = await ctx.model.CheckRecord.updateOne(
          { _id: id },
          {
            $set: {
              businessLicense,
            },
          }
        );
      }
      if (practicingLicense.length > 0) {
        res = await ctx.model.CheckRecord.updateOne(
          { _id: id },
          {
            $set: {
              practicingLicense,
            },
          }
        );
      }
      if (diagnosisLicense.length > 0) {
        res = await ctx.model.CheckRecord.updateOne(
          { _id: id },
          {
            $set: {
              diagnosisLicense,
            },
          }
        );
      }
      if (qualityControl.length > 0) {
        res = await ctx.model.CheckRecord.updateOne(
          { _id: id },
          {
            $set: {
              qualityControl,
            },
          }
        );
      }
      if (checkReport.length > 0) {
        res = await ctx.model.CheckRecord.updateOne(
          { _id: id },
          {
            $set: {
              checkReport,
            },
          }
        );
      }
      if (recordTable.length > 0) {
        res = await ctx.model.CheckRecord.updateOne(
          { _id: id },
          {
            $set: {
              recordTable,
            },
          }
        );
      }
      // const res = await ctx.model.CheckRecord.updateOne({_id: id}, {$set: {
      //   businessLicense,
      //   practicingLicense,
      //   diagnosisLicense
      // }})
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: "成功",
        status: 200,
      });
    } catch (error) {
      console.log(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },

  async getRecordList(ctx) {
    try {
      const res = await ctx.service.checkOrgRecord.getRecordList(ctx.query);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: "成功",
        status: 200,
      });
    } catch (error) {
      console.log(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },

  async getOneRecord(ctx) {
    try {
      const res = await ctx.service.checkOrgRecord.getOneRecord(ctx.query);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: "成功",
        status: 200,
      });
    } catch (error) {
      console.log(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },

  // getRecordInfo
  async getRecordInfo(ctx) {
    try {
      const res = await ctx.model.CheckRecord.find({
        recordType: '首次备案',
        org_id: ctx.session.physicalExamUserInfo.EnterpriseID,
        isLog: false
      })
      const res2 = await ctx.model.CheckRecord.find({
        recordStatus: '待审核',
        org_id: ctx.session.physicalExamUserInfo.EnterpriseID,
        isLog: false
      })
      ctx.helper.renderSuccess(ctx, {
        data: {
          first: res,
          status:res2 
        },
        message: "成功",
        status: 200,
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },


  async saveRecord(ctx) {
    try {
      const res = await ctx.service.checkOrgRecord.saveRecord(ctx.request.body);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: "成功",
        status: 200,
      });
    } catch (error) {
      console.log(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },

  async submitRecord(ctx) {
    try {
      const res = await ctx.service.checkOrgRecord.submitRecord(
        ctx.request.body
      );
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: "成功",
        status: 200,
      });
    } catch (error) {
      console.log(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },

  // checkOrgRecord/cancelRecord

  async cancelRecord(ctx) {
    try {
      const res = await ctx.service.checkOrgRecord.cancelRecord(
        ctx.request.body
      );
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: "成功",
        status: 200,
      });
    } catch (error) {
      console.log(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },

  async addTechnician(ctx) {
    try {
      const res = await ctx.service.checkOrgRecord.addTechnician(
        ctx.request.body
      );
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: "成功",
        status: 200,
      });
    } catch (error) {
      console.log(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },

  async getTechnicianList(ctx) {
    try {
      const res = await ctx.service.checkOrgRecord.getTechnicianList(ctx.query);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: "成功",
        status: 200,
      });
    } catch (error) {
      console.log(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },

  async removeTechnician(ctx) {
    try {
      const res = await ctx.service.checkOrgRecord.removeTechnician(
        ctx.request.body
      );
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: "成功",
        status: 200,
      });
    } catch (error) {
      console.log(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },

  async addInstrument(ctx) {
    try {
      const res = await ctx.service.checkOrgRecord.addInstrument(
        ctx.request.body
      );
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: "成功",
        status: 200,
      });
    } catch (error) {
      console.log(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },

  async getInstruments(ctx) {
    try {
      const res = await ctx.service.checkOrgRecord.getInstruments(ctx.query);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: "成功",
        status: 200,
      });
    } catch (error) {
      console.log(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },

  async removeInstrument(ctx) {
    try {
      const res = await ctx.service.checkOrgRecord.removeInstrument(
        ctx.request.body
      );
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: "成功",
        status: 200,
      });
    } catch (error) {
      console.log(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },

  // uploadMaterial
  async uploadMaterial(ctx) {
    const parts = ctx.multipart({ autoFields: true });

    let part;
    const attachment = [];
    try {
      while ((part = await parts()) != null) {
        if (!part.filename) {
          return;
        }
        // 获取其他formData参数
        // 处理文件流
        const timestamp = new Date().getTime();
        const randomCode = Math.floor(Math.random() * 1000000)
          .toString()
          .padStart(6, "0");
        // 拿到文件后缀
        const extname = path.extname(part.filename);
        // 拼接成新的文件名
        const filename = timestamp + randomCode + extname;
        // 处理得到静态资源路径地址
        const filePath = path.join(ctx.app.config.upload_record_path);

        const target = path.resolve(filePath, filename);

        if (!fs.existsSync(filePath)) {
          fs.mkdirSync(filePath, { recursive: true });
        }

        // 读流
        const writeStream = fs.createWriteStream(target);
        try {
          // 写流
          await awaitWriteStream(part.pipe(writeStream));
          attachment.push({
            name: part.filename,
            url: filename,
            type: part.fieldname,
          });
        } catch (error) {
          await sendToWormhole(part);
          writeStream.destroy();
          throw error;
        }
      }

      const deleteData = await parts.field.deleteFile;
      if (deleteData) {
        const deleteFile = JSON.parse(deleteData);
        for (let index = 0; index < deleteFile.length; index++) {
          const fileName =
            deleteFile[index].url.split("/")[
              deleteFile[index].url.split("/").length - 1
            ];
          const filePath = path.join(
            ctx.app.config.upload_record_path,
            fileName
          );
          if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
          }
        }
      }

      // *************************************************************
      ctx.helper.renderSuccess(ctx, {
        data: attachment,
      });
      //* ****************************************************************
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },

  async downRecordTable(ctx) {
    try {
      const res = await ctx.service.checkOrgRecord.downRecordTable(
        ctx.request.body
      );
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: "下载成功",
        status: 200,
      });
    } catch (error) {
      console.log(error);
      ctx.helper.renderFail(ctx, {
        message: error.message,
      });
    }
  },
};

module.exports = checkOrgRecordController;
