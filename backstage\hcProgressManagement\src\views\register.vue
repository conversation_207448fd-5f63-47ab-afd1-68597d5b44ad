<template>
  <div class="page-container">
    <div class="main-content">
      <div class="content-wrapper">
        <!-- 左侧表单区域 -->
        <div class="form-section">
          <div class="form-header">
            <h2 class="form-title">体检登记表</h2>
            <div class="avatar-container">
              <div class="avatar-wrapper">
                <img v-if="formData.avatar" :src="formData.avatar" class="avatar-image" alt="头像" />
                <i v-else class="el-icon-s-custom" style="font-size:96px"></i>
              </div>
              <button class="avatar-upload-btn rounded-button">
                <i class="el-icon-camera-solid"></i>
              </button>
            </div>
          </div>
          <div class="form-grid">
            <!-- 输入编号自动获取基础信息 -->
            <div class="form-group">
              <label class="form-label">体检编号</label>
              <input type="text" v-model="formData.checkNo" class="form-input" placeholder='填入后获取信息' />
            </div>
            <div class="form-group">
              <label class="form-label">工号</label>
              <input type="text" v-model="formData.employeeID" class="form-input" disabled />
            </div>
            <div class="form-group">
              <label class="form-label">姓名</label>
              <input type="text" v-model="formData.name" class="form-input" />
            </div>
            <div class="form-group">
              <label class="form-label">性别</label>
              <div class="radio-group">
                <label class="radio-label">
                  <input type="radio" v-model="formData.gender" value="1" class="radio-input" />
                  <span class="radio-text">男</span>
                </label>
                <label class="radio-label">
                  <input type="radio" v-model="formData.gender" value="2" class="radio-input" />
                  <span class="radio-text">女</span>
                </label>
              </div>
            </div>
            <div class="form-group">
              <label class="form-label">出生日期</label>
              <el-date-picker v-model="formData.birthDate" type="date" class="form-input" style="width: 100%;" />
            </div>
            <div class="form-group">
              <label class="form-label">证件类型</label>
              <el-select v-model="formData.idType" class="form-input">
                <el-option v-for="item in idTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </div>
            <div class="form-group">
              <label class="form-label">证件号码</label>
              <input type="text" v-model="formData.idNumber" class="form-input" />
            </div>

            <div class="form-group">
              <label class="form-label">婚姻状况</label>
              <el-select v-model="formData.maritalStatus" class="form-input">
                <el-option v-for="item in maritalStatusOptions" :key="item.value" :label="item.label"
                  :value="item.value" />
              </el-select>
            </div>
            <div class="form-group">
              <label class="form-label">手机号码</label>
              <input type="text" v-model="formData.phone" class="form-input" />
            </div>

            <div class="form-group">
              <label class="form-label">紧急联系人</label>
              <input type="text" v-model="formData.emergencyContact" class="form-input" />
            </div>
            <div class="form-group">
              <label class="form-label">紧急联系人电话</label>
              <input type="text" v-model="formData.emergencyPhone" class="form-input" />
            </div>
            <div class="form-group">
              <label class="form-label">体检类型</label>
              <el-select v-model="formData.checkType" class="form-input">
                <el-option v-for="item in checkTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </div>
            <div class="form-group">
              <label class="form-label">检查类型</label>
              <el-select v-model="formData.examType" class="form-input">
                <el-option v-for="item in examTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </div>
            <div class="form-group">
              <label class="form-label">用人单位</label>
              <!-- 模糊查询，绑定的ID应该是体检端维护的企业ID -->
              <el-select v-model="formData.EnterpriseID" class="form-input" :disabled="formData.checkNo.length > 0">
                <el-option v-for="item in enterpriseOptions" :key="item.EnterpriseID" :label="item.cname"
                  :value="item.EnterpriseID" />
              </el-select>
            </div>
            <div class="form-group">
              <label class="form-label">部门</label>
              <input type="text" v-model="formData.department" class="form-input" />
            </div>
            <div class="form-group">
              <label class="form-label">工种</label>
              <input type="text" v-model="formData.workType" class="form-input" />
            </div>
            <div class="form-group">
              <label class="form-label">住址</label>
              <input type="text" v-model="formData.address" class="form-input" />
            </div>

            <!-- hazardFactors接触的危害因素 -->
            <div class="form-group">
              <label class="form-label">接触危害因素</label>
              <el-select v-show="type !== 'normal'" style="width: 100%" v-model="formData.hazardFactors" filterable
                remote multiple reserve-keyword placeholder="请输入接触的危害因素名称" :remote-method="remoteMethod"
                :loading="loading">
                <el-option v-for="item in harmFactors" :key="item._id" :label="item.name" :value="item.name">
                  <span style="float: left; margin-right: 7px">{{ item.name }}</span>
                  <span v-if="item.otherName" style="color: #8492a6; font-size: 13px">
                    (别名:{{ item.otherName }})
                  </span>
                </el-option>
              </el-select>
            </div>
            <!-- checkHazardFactors体检的危害因素 -->
            <div class="form-group">
              <label class="form-label">体检的危害因素</label>
              <el-select v-show="type !== 'normal'" style="width: 100%" v-model="formData.checkHazardFactors" filterable
                remote multiple reserve-keyword placeholder="请输入危害因素名称" :remote-method="remoteMethod" :loading="loading"
                ref="select" @change="handleSelectChange">
                <el-option v-for="item in harmFactors" :key="item._id" :label="item.name" :value="item.name">
                  <span style="float: left; margin-right: 7px">{{ item.name }}</span>
                  <span v-if="item.otherName" style="color: #8492a6; font-size: 13px">
                    (别名:{{ item.otherName }})
                  </span>
                </el-option>
              </el-select>

            </div>

            <div class="form-group">
              <label class="form-label">总工龄</label>
              <div class="work-years-input">
                <el-input-number v-model="formData.totalWorkYears" :min="0" class="year-input" />
                <span class="unit">年</span>
                <el-input-number v-model="formData.totalWorkMonths" :min="0" :max="11" class="month-input" />
                <span class="unit">月</span>
              </div>
            </div>
            <div class="form-group">
              <label class="form-label">接触工龄</label>
              <div class="work-years-input">
                <el-input-number v-model="formData.exposureWorkYears" :min="0" class="year-input" />
                <span class="unit">年</span>
                <el-input-number v-model="formData.exposureWorkMonths" :min="0" :max="11" class="month-input" />
                <span class="unit">月</span>
              </div>
            </div>
            <div class="form-group">
              <label class="form-label">是否复查</label>
              <el-switch v-model="formData.isRecheck" />
            </div>
            <div class="form-group" v-if="formData.isRecheck">
              <label class="form-label">复查编号</label>
              <input type="text" v-model="formData.recheckNo" class="form-input" />
            </div>
          </div>
        </div>
        <!-- 右侧检查项目区域 -->
        <div class="exam-section">
          <h2 class="section-title">已选项目 ({{ selectedItems.length }})</h2>
          <div class="selected-items">
            <el-table :data="selectedItems" style="width: 100%">
              <el-table-column prop="deptName" label="科室"></el-table-column>
              <el-table-column prop="itemName" label="项目名称"></el-table-column>
              <el-table-column prop="itemPrice" label="价格"></el-table-column>
              <el-table-column label="操作" width="60">
                <template slot-scope="scope">
                  <el-button type="danger" size="mini" class="remove-btn" @click="deleteItem(scope.row)">
                    <i class="el-icon-delete"></i>
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="total-price">
            <div class="price-wrapper">
              <span class="price-label">总计金额</span>
              <span class="price-value">¥{{ totalPrice }}</span>
            </div>
          </div>
          <h2 class="section-title">选择检查项目</h2>
          <div class="exam-items">
            <el-tabs tab-position="left" v-model="selectedDept" style="height: 360px">
              <el-tab-pane v-for="dept in departments" :key="dept._id" :label="dept.departmentName" :name="dept._id">
                <div class="items-list">
                  <div class="items-wrapper">
                    <div v-for="item in currentDeptItems" :key="item.id" class="item">
                      <span class="item-name">{{ item.itemName }}</span>
                      <div class="item-price-action">
                        <span class="item-price">¥{{ item.itemPrice }}</span>
                        <el-button size="small" :type="item.selected ? 'primary' : ''"
                          @click="toggleItem(item, dept.departmentName)">
                          {{ item.selected ? '已选' : '选择' }}
                        </el-button>
                      </div>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>

          <div style="margin-top: 24px;width: 100%;display: flex;flex-direction: row-reverse;">
            <el-button type="primary" icon="el-icon-s-promotion" @click="submit">提交</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { createRegister, getRegisterDetail, updateRegister, getRegisterDetailByCheckNo, getDepartmentAndChargeItemsList, getMatainCompanyList, findHarmFactors, getRegisterDetailByIdNumber } from '@/api';
import moment from 'moment';
export default {
  data() {
    return {
      loading: false,
      formData: {
        checkNo: '',
        name: '',
        gender: '1',
        avatar: '',
        idType: '1',
        idNumber: '',
        birthDate: '',
        maritalStatus: '10',
        phone: '',
        address: '',
        emergencyContact: '',
        emergencyPhone: '',
        checkType: '1',
        EnterpriseID: '',
        department: '',
        workType: '',
        hazardFactors: '',
        checkHazardFactors: [],
        totalWorkYears: 0,
        totalWorkMonths: 0,
        exposureWorkYears: 0,
        exposureWorkMonths: 0,
        examType: '1',
        isRecheck: false,
        recheckNo: '',
      },
      idTypeOptions: [
        { value: '1', label: '居民身份证' },
        { value: '2', label: '居民户口簿' },
        { value: '3', label: '护照' },
        { value: '4', label: '军官证' },
        { value: '5', label: '驾驶证' },
        { value: '6', label: '港澳居民来往内地通行证' },
        { value: '7', label: '台湾居民来往内地通行证' },
        { value: '99', label: '其他法定有效证件' }
      ],
      maritalStatusOptions: [
        { value: '10', label: '未婚' },
        { value: '20', label: '已婚' },
        // { value: '21', label: '初婚' },
        // { value: '22', label: '再婚' },
        // { value: '23', label: '复婚' },
        { value: '30', label: '丧偶' },
        { value: '40', label: '离婚' },
        { value: '90', label: '未说明的婚姻状况' }
      ],
      checkTypeOptions: [
        { value: '1', label: '职业健康体检' },
        { value: '2', label: '一般体检' }
      ],
      examTypeOptions: [
        { value: '1', label: '岗前' },
        { value: '2', label: '在岗' },
        { value: '3', label: '离岗' },
        { value: '4', label: '离岗后' },
        { value: '5', label: '应急' }
      ],
      harmFactors: [],// 危害因素可选项
      originalCheckHazardFactors: [], // 原始的危害因素对象数组，包含code字段
      enterpriseOptions: [],
      selectedDept: '', // 当前选中的科室 _id
      departments: [], // 所有的科室
      examItems: [], // 所有的科室中全部的检查项目
      selectedItems: [] // 选中的检查项目 { _id, deptId, name, price, selected }
    };
  },
  computed: {
    // selectedItems() {
    //   return this.examItems.filter(item => item.selected);
    // },
    totalPrice() {
      return this.selectedItems.reduce((sum, item) => parseFloat(sum) + parseFloat(item.itemPrice), 0);
    },
    currentDeptItems() {
      // return this.examItems.filter(item => item.deptId === this.selectedDept);
      const res = this.departments.find(dept => dept._id === this.selectedDept);
      return res ? res.departmentCheckItem : [];
    }
  },
  watch: {
    'formData.checkNo': {
      handler: async function (newVal) {
        console.log('formData.checkNo', newVal)
        if (newVal.length === 16 && !this.formData.name && !this.formData._id) {
          const res = await getRegisterDetailByCheckNo({ checkNo: newVal })
          if (res.status === 200) {
            // 获取响应数据
            const responseData = res.data

            const formattedData = this.formatResData(responseData)

            this.formData = formattedData
          }
        }
      }
    },

    'formData.idNumber': {
      handler: async function (newVal) {
        console.log('formData.idNumber', newVal)
        if (newVal.length === 18 && !this.formData.name && !this.formData._id) {
          const res = await getRegisterDetailByIdNumber({ idNumber: newVal })
          if (res.status === 200) {
            // 获取响应数据
            const responseData = res.data

            const formattedData = this.formatResData(responseData)

            this.formData = formattedData
          }
        }
      }
    }

  },
  methods: {

    // 处理响应数据
    formatResData(response) {
      // 获取响应数据
      const responseData = JSON.parse(JSON.stringify(response))

      // 处理checkHazardFactors，将对象数组转换为名称数组
      if (responseData.checkHazardFactors && Array.isArray(responseData.checkHazardFactors)) {
        // 将对象数组转换为名称数组，但同时保存原始对象数组以便于提交
        this.originalCheckHazardFactors = JSON.parse(JSON.stringify(responseData.checkHazardFactors));
        responseData.checkHazardFactors = responseData.checkHazardFactors.map(item => item.name)
      }

      // 确保hazardFactors是字符串数组
      if (responseData.hazardFactors) {
        // 如果是字符串，将其转换为数组
        if (typeof responseData.hazardFactors === 'string') {
          responseData.hazardFactors = responseData.hazardFactors.split('、').map(item => item.trim())
        }
        // 如果是对象数组，提取name属性
        else if (Array.isArray(responseData.hazardFactors) && responseData.hazardFactors.length > 0 && typeof responseData.hazardFactors[0] === 'object') {
          responseData.hazardFactors = responseData.hazardFactors.map(item => item.name || item)
        }
      }
      return responseData
    },

    handleSelectChange(val) {
      console.log('Selected hazard factors:', val)
      // val is now an array of strings (names)
    },
    // 获取危害因素
    async getHarmFactor(query) {
      let res = await findHarmFactors(query)
      if (res.status === 200) {
        this.harmFactors = res.data.data.map(item => {
          return {
            _id: item._id,
            name: item.harmFactorName,
            code: item.code,
            otherName: item.otherName,
          }
        })
      }
    },
    async remoteMethod(query) {
      if (query !== "") {
        this.loading = true
        await this.getHarmFactor({ query: { name: query } })
        this.loading = false
      } else {
        await this.getHarmFactor({ pageSize: 999, query: { name: '' } })
      }
    },
    async getCompanyList() {
      const res = await getMatainCompanyList({ pageNum: 1, pageSize: 999 })
      this.enterpriseOptions = res.data.res
    },
    deleteItem(item) {
      const index = this.selectedItems.findIndex(selectedItem => selectedItem._id === item._id);
      this.selectedItems.splice(index, 1);
      const dept = this.departments.find(dept => dept._id === item.deptId);
      const project = dept.departmentCheckItem.find(project => project._id === item._id);
      project.selected = false;
    },
    toggleItem(item, deptName) {
      item.selected = !item.selected;
      item.deptName = deptName
      if (item.selected) {
        this.selectedItems.push(item);
        console.log('item', item)
      } else {
        const index = this.selectedItems.findIndex(selectedItem => selectedItem._id === item._id);
        this.selectedItems.splice(index, 1);
      }
    },
    async getRegisterDetailData(params) {
      const res = await getRegisterDetail(params)
      if (res.status === 200) {
        // 获取响应数据
        const responseData = res.data

        // 根据身份证号自动填入生日
        if (responseData.idNumber && responseData.idNumber.length === 18) {
          responseData.birthDate = moment(responseData.idNumber.slice(6, 14))
        }

        // 处理checkHazardFactors，将对象数组转换为名称数组
        if (responseData.checkHazardFactors && Array.isArray(responseData.checkHazardFactors)) {
          // 将对象数组转换为名称数组，但同时保存原始对象数组以便于提交
          // 在内存中保存完整的危害因素对象数组
          this.originalCheckHazardFactors = JSON.parse(JSON.stringify(responseData.checkHazardFactors));
          responseData.checkHazardFactors = responseData.checkHazardFactors.map(item => item.name)
        }

        // 确保hazardFactors是字符串数组
        if (responseData.hazardFactors) {
          // 如果是字符串，将其转换为数组
          if (typeof responseData.hazardFactors === 'string') {
            responseData.hazardFactors = responseData.hazardFactors.split('、').map(item => item.trim())
          }
          // 如果是对象数组，提取name属性
          else if (Array.isArray(responseData.hazardFactors) && responseData.hazardFactors.length > 0 && typeof responseData.hazardFactors[0] === 'object') {
            responseData.hazardFactors = responseData.hazardFactors.map(item => item.name || item)
          }
        }

        this.formData = responseData

        // 映射选中的检查项目
        this.formData.checkDepartments.forEach(Depart => {
          Depart.checkProjects.forEach(Project => {
            this.selectedItems.push({
              _id: Project.projectId,
              deptId: Depart.departmentId,
              deptName: Depart.departmentName,
              itemName: Project.projectName,
              itemPrice: Project.projectPrice,
            })
          })
        });
      }
    },
    async submit() {
      try {
        let res = null
        const data = JSON.parse(JSON.stringify(this.formData))
        data.totalPrice = this.totalPrice
        // selected为选中的检查项目和科室
        data.checkDepartments = []
        // hazardFactors已经是字符串数组，不需要额外处理

        // 将checkHazardFactors字符串数组转换为对象数组，以符合API要求
        if (this.originalCheckHazardFactors && this.originalCheckHazardFactors.length > 0 && data._id) {
          // 如果是编辑模式且有原始数据，需要处理删除和新增的情况
          // 先找出原始数据中保留的项
          const remainingFactors = this.originalCheckHazardFactors.filter(item =>
            data.checkHazardFactors.includes(item.name)
          );

          // 再找出新增的项
          const newFactorNames = data.checkHazardFactors.filter(name =>
            !this.originalCheckHazardFactors.some(item => item.name === name)
          );

          // 将新增的项转换为对象
          const newFactors = newFactorNames.map(name => {
            const harmFactor = this.harmFactors.find(item => item.name === name);
            return {
              name: name.trim(),
              code: harmFactor ? harmFactor.code : ''
            }
          });

          // 合并保留的和新增的
          data.checkHazardFactors = [...remainingFactors, ...newFactors];
        } else if (Array.isArray(data.checkHazardFactors)) {
          // 如果是新增模式或没有原始数据
          data.checkHazardFactors = data.checkHazardFactors.map(name => {
            // 从危害因素列表中查找对应的code
            const harmFactor = this.harmFactors.find(item => item.name === name);
            return {
              name: name.trim(),
              code: harmFactor ? harmFactor.code : ''
            }
          })
        } else if (typeof data.checkHazardFactors === 'string') {
          data.checkHazardFactors = data.checkHazardFactors.split('、').map(item => {
            const name = item.trim();
            // 从危害因素列表中查找对应的code
            const harmFactor = this.harmFactors.find(hf => hf.name === name);
            return {
              name: name,
              code: harmFactor ? harmFactor.code : ''
            }
          })
        }

        this.departments.forEach(dept => {
          // 如果当前科室下有选中的检查项目
          if (dept.departmentCheckItem.some(item => item.selected)) {
            data.checkDepartments.push({
              departmentId: dept._id,
              departmentName: dept.departmentName,
              checkProjects: dept.departmentCheckItem.filter(item => item.selected).map(item => {
                return {
                  projectId: item._id,
                  projectName: item.itemName,
                  projectPrice: item.itemPrice
                }
              })
            })
          }
        })

        if (!data.checkNo) { // 无编号则为其新增
          const { _id, ...rest } = data
          res = await createRegister(rest)
        } else { // 有编号走更新
          res = await updateRegister(data)
        }
        if (res.status === 200) {
          this.$message.success('提交成功')
          this.$router.push('/admin/hcProgressManagement/register')
        }
      } catch (error) {
        this.$message.error(error.message)
      }
    },
    async getDepartmentAndChargeItemsList() {
      const res = await getDepartmentAndChargeItemsList()
      if (res.status === 200) {
        const departments = res.data.map(dept => {
          return {
            ...dept,
            departmentCheckItem: dept.departmentCheckItem.map(item => {
              const selected = this.selectedItems.some(selectedItem => {
                // _id 和 科室名称都相同
                return selectedItem._id === item._id && selectedItem.deptName === dept.departmentName
              });
              return {
                ...item,
                selected
              }
            })
          }
        })
        this.departments = departments
        this.selectedDept = departments[0]._id;
      }
    }
  },
  async created() {
    // 获取路由中的参数 _id
    const _id = this.$route.query._id;
    if (_id) {
      await this.getRegisterDetailData({ _id })
    }
    await this.getDepartmentAndChargeItemsList()

    await this.getCompanyList()

    await this.getHarmFactor({ pageSize: 999, query: { name: '' } })
  }
};
</script>
<style scoped>
.page-container {
  padding: 24px;
  background-color: #f9fafb;
  width: 100%;
}

.main-content {
  width: 1440px;
  width: 100%;
  margin: 0 auto;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.content-wrapper {
  display: flex;
  padding: 32px;
}

.form-section {
  width: 50%;
  padding-right: 32px;
  border-right: 1px solid #e5e7eb;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.form-title {
  font-size: 24px;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 24px;
}

.avatar-container {
  position: relative;
}

.avatar-wrapper {
  width: 96px;
  height: 96px;
  border-radius: 8px;
  background-color: #f3f4f6;
  overflow: hidden;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  font-size: 32px;
  color: #9ca3af;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.avatar-upload-btn {
  position: absolute;
  bottom: 0;
  right: 0;
  background-color: #3b82f6;
  color: white;
  padding: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.avatar-upload-btn:hover {
  background-color: #2563eb;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.form-input {
  width: 100%;
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  outline: none;
}

.form-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.work-years-input {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  background-color: #f9fafb;
  border-radius: 8px;
  border: 1px solid #d1d5db;
}

.year-input,
.month-input {
  width: 100px !important;
}

.year-input :deep(.el-input-number__decrease),
.year-input :deep(.el-input-number__increase),
.month-input :deep(.el-input-number__decrease),
.month-input :deep(.el-input-number__increase) {
  background-color: transparent;
  border-color: #d1d5db;
}

.year-input :deep(.el-input__inner),
.month-input :deep(.el-input__inner) {
  text-align: center;
  padding-left: 30px;
  padding-right: 30px;
}

.unit {
  color: #374151;
  font-size: 14px;
  padding: 0 4px;
  flex-shrink: 0;
}

/* Element UI 组件样式覆盖 */
:deep(.el-select .el-input__wrapper),
:deep(.el-date-editor.el-input__wrapper) {
  box-shadow: none !important;
  padding: 0;
}

:deep(.el-select),
:deep(.el-date-editor) {
  border: 1px solid #d1d5db !important;
  border-radius: 8px;
  padding: 0;
}

:deep(.el-select:hover),
:deep(.el-date-editor:hover) {
  border-color: #3b82f6 !important;
}

:deep(.el-select.is-focus),
:deep(.el-date-editor.is-focus) {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

:deep(.el-select .el-input__inner),
:deep(.el-date-editor .el-input__inner) {
  padding: 8px 16px;
  height: auto;
  line-height: normal;
}

:deep(.el-input__suffix) {
  right: 8px;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-date-editor) {
  width: 100%;
}

:deep(.el-input-number) {
  width: 100px;
}

:deep(.el-input-number .el-input__inner) {
  padding-left: 30px;
  padding-right: 30px;
}

:deep(.el-switch) {
  margin-top: 8px;
}

.radio-group {
  display: flex;
  gap: 16px;
}

.radio-label {
  display: inline-flex;
  align-items: center;
  cursor: pointer;
}

.radio-input {
  accent-color: #3b82f6;
  margin-right: 8px;
}

.exam-section {
  width: 50%;
  padding-left: 32px;
}

.section-title {
  font-size: 20px;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 16px;
}

.selected-items {
  height: 360px;
  background-color: #f9fafb;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  overflow: auto;
}

.selected-items-title {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 16px;
}

.items-table {
  width: 100%;
  border-collapse: collapse;
}

.items-table th {
  padding: 8px 16px;
  text-align: left;
  background-color: #f3f4f6;
}

.items-table td {
  padding: 12px 16px;
  border-bottom: 1px solid #e5e7eb;
}

.items-table .price {
  text-align: right;
  color: #3b82f6;
}

.items-table .action {
  text-align: center;
}

.remove-btn {
  /* color: #ef4444; */
  color: #FFF
}

.remove-btn:hover {
  /* color: #dc2626; */
  color: #FFF
}

.total-price {
  background-color: white;
  padding: 16px;
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.price-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price-label {
  font-size: 18px;
  font-weight: 500;
}

.price-value {
  font-size: 24px;
  font-weight: bold;
  /* color: #3b82f6; */
  color: #f56c6c;

}

.exam-items {
  border: 1px solid #e4e7ed;
  border-radius: 4px;

  :deep(.el-tabs) {
    height: 360px;

    .el-tabs__header {
      margin-right: 0;
    }

    .el-tabs__nav {
      width: 150px;
    }

    .el-tabs__item {
      text-align: left;
      height: 40px;
      line-height: 40px;
      padding: 0 20px;

      &.is-active {
        background-color: #f5f7fa;
      }
    }
  }

  .department-item.selected {
    background-color: #eff6ff;
    color: #3b82f6;
  }

  .department-item.selected {
    background-color: #eff6ff;
    color: #3b82f6;
  }

  .items-list {
    height: 360px;
    overflow-y: auto;
    padding: 20px;
    background-color: #fff;

    .items-wrapper {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 15px;
    }

    .item {
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      padding: 15px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .item-name {
        font-size: 14px;
        color: #606266;
      }

      .item-price-action {
        display: flex;
        align-items: center;
        gap: 10px;

        .item-price {
          color: #f56c6c;
          font-weight: bold;
        }
      }
    }

    .select-btn:not(.selected) {
      border: 1px solid #d1d5db;
      color: #6b7280;
    }

    .select-btn:not(.selected) {
      border: 1px solid #d1d5db;
      color: #6b7280;
    }
  }

  .select-btn.selected {
    background-color: #3b82f6;
    color: white;
  }

  .select-btn.selected {
    background-color: #3b82f6;
    color: white;
  }
}

.rounded-button {
  border-radius: 4px;
}

.rounded-button {
  border-radius: 4px;
}
</style>