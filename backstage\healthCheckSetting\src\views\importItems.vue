<template>
  <div class="app-container importConstruction">
    <div style="display: flex">
      <page-header title="工作场所excel导入"> </page-header>
      <el-link icon="el-icon-question" type="primary" @click="info">引导</el-link>
    </div>

    <upload-excel-component
      :submit="submit"
      :on-success="handleSuccess"
      :before-upload="beforeUpload"
      @downloadTemplate="downloadTemplate"
      @deleteMany="deleteMany"
      :multipleSelection="multipleSelection"
    />
    <el-form class="excelForm">
      <pl-table
        @selection-change="handleSelectionChange"
        :data="tableData"
        border
        height="600px"
        use-virtual
        highlight-current-row
        style="width: 100%; margin-top: 20px"
      >
        <pl-table-column type="selection" width="55"> </pl-table-column>
        <pl-table-column
          v-for="item of tableHeader"
          :key="item"
          :prop="item"
          :label="item"
        >
          <template slot-scope="scope">
            <el-form-item>
              <el-input v-model="tableData[scope.$index][item]"></el-input>
            </el-form-item>
          </template>
        </pl-table-column>
        <pl-table-column label="操作">
          <template slot-scope="scope">
            <el-button type="danger" size="mini" @click="deleteItem(scope.$index)"
              >删除</el-button
            >
          </template>
        </pl-table-column>
      </pl-table>
    </el-form>
    <import-tips
      :requireError="requireError"
      :dialogVisible.sync="dialogTip"
      :touchTimeErr="touchTimeErr"
    >
    </import-tips>
    <el-dialog
      :close-on-click-modal="false"
      :visible.sync="showSaveLoad"
      :show-close="false"
      width="0"
    >
      <ProgressLoad
        :percentage="percentage"
        :percentageItem="percentageItem"
        :tableDataLength="ProgressLength"
      ></ProgressLoad>
    </el-dialog>
  </div>
</template>

<script>
import ProgressLoad from "@/components/ProgressLoad/ProgressLoad.vue";
import Driver from "driver.js";
import { PlTable, PlTableColumn } from "pl-table";
import io from "socket.io-client";
import "driver.js/dist/driver.min.css";
import ImportTips from "@/components/importTips/index";
import pageHeader from "@/components/pageHeader.vue";
import UploadExcelComponent from "@/components/UploadExcel/index.vue";
import { findHarmFactors } from '@/api/index'
const driver = new Driver({
  allowClose: false,
  doneBtnText: "结束引导",
  closeBtnText: "关闭引导",
  nextBtnText: "下一步",
  prevBtnText: "上一步",
});
export default {
  name: "UploadExcel",
  components: {
    PlTable,
    PlTableColumn,
    UploadExcelComponent,
    pageHeader,
    ImportTips,
    ProgressLoad,
  },
  data() {
    return {
      showSaveLoad: false,
      driver: null,
      dialogTip: false,
      requireError: [], //存在必填项未填
      touchTimeErr: "", //接触时间格式错误
      dialogVisible: false,
      tableData: [],
      tableHeader: [],
      importData: [],
      multipleSelection: [],
      socket: null,
      socketID: null,
      percentage: 0, // 进度条进度
      percentageItem: 0, // 当前第几个
      ProgressRatio: 0, // 每个的进度
      ProgressLength: 0, // 进度总的个数
    };
  },
  created() {
    setTimeout(() => {
      this.info();
    }, 50);
  },
  beforeDestroy() {
    if (this.socket) {
      this.socket.disconnect();
    }
  },
  methods: {
    // 获取危害因素名称
    async checkHarmFactor(name) {
      const res = await findHarmFactors({ query: { name, strict: true } });
      if (res.status === 200 && Array.isArray(res.data.data) && res.data.data.length > 0) {
        return true;
      }
      return false;
    },
    // 建立 socket 连接
    openSocket() {
      this.socket = io(document.location.host, { ioreconnection: false });
      const socket = this.socket;
      console.log(socket);
      this.socket.on("connect", (res) => {
        console.log("connect!");

        // 建立 websocket 连接
        socket.emit("openProgress");

        // 监听 进度条 变化事件
        socket.on("progress", async (msg) => {
          if (this.percentageItem + 1 === this.importData.length) {
            this.percentage = 100;
          } else {
            this.percentage += this.ProgressRatio;
          }
          this.percentageItem += 1;
        });

        // 监听 addMillsEnd 结束事件
        socket.on("addMillsEnd", async (data) => {
          if (data && data.errMsg) {
            this.$message.error("出错了");
            this.socket.disconnect();
            this.socket = null;
          } else {
            // 关闭 进度条
            if (data === this.importData.length) {
              this.$message({
                message: `成功导入${data}条数据`,
                type: "success",
                duration: 4000,
              });
              this.importData = [];
              setTimeout(() => {
                this.$router.push({ name: "workspace" });
              }, 800);
            } else {
              setTimeout(() => {
                this.showSaveLoad = false;
              }, 800);
              this.$message({
                message: `${data}条保存成功，剩下${
                  this.importData.length - data
                }条保存失败`,
                type: "warning",
              });
            }
          }
        });
        // 监听 连接 成功的讯息
        socket.on("id", async (msg) => {
          this.socketID = msg;
          this.handleAddMills();
        });
      });
    },
    async info() {
      driver.defineSteps([
        {
          element: ".downloadBtn",
          popover: {
            className: "first-step-popover-class",
            title: "下载模板（1/4）",
            description: "提供Excel模板下载，您可以参照Excel模板来完善您的Excel",
            position: "bottom",
          },
        },
        {
          element: ".importExcel",
          popover: {
            className: "first-step-popover-class",
            title: "导入Excel（2/4）",
            description: "点击导入，选择对应的Excel文件，即可导入成功",
            position: "bottom",
          },
        },
        {
          element: ".excelForm",
          popover: {
            className: "first-step-popover-class",
            title: "检查信息（3/4）",
            description: "查看导入的Excel工作场所信息，可点击对应的信息进行编辑修改",
            position: "top",
          },
        },
        {
          element: ".submitBtn",
          popover: {
            className: "first-step-popover-class",
            title: "保存信息（4/4）",
            description: "信息检查完毕后，保存即可提交工作场所信息",
            position: "left",
          },
        },
      ]);
      driver.start();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    deleteMany() {
      this.$confirm("确定删除所选数据？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.multipleSelection.forEach((item) => {
          this.tableData.splice(this.tableData.indexOf(item), 1);
        });
      });
    },
    deleteItem(i) {
      this.tableData.splice(i, 1);
    },
    downloadTemplate() {
      const a = document.createElement("a"); // 创建a标签
      a.setAttribute("download", "工作场所模板.xlsx");
      const href = '/static/dataTemplate/工作场所模板_new.xlsx';
      // a.setAttribute("href", localStorage.millExcelTemp); // href链接
      a.setAttribute("href", href); // href链接
      a.click(); // 自执行点击事件
      driver.isActivated && driver.moveNext();
    },
    beforeUpload(file) {
      const isLt1M = file.size / 1024 / 1024 < 1;
      if (isLt1M) {
        return true;
      }
      this.$message({
        message: "Please do not upload files larger than 1m in size.",
        type: "warning",
      });
      return false;
    },
    async submit() {
      let requireError = [];
      let touchTimeErr = "";
      driver.isActivated && driver.reset();
      if (this.tableData.length === 0) {
        return this.$message.warning("请先上传excel文件");
      }
      const importData = this.tableData.map(async (item, index) => {
        if(!item.车间){
          requireError.push(`第${index + 1}行的车间名称未填写`);
        }
        if(!item.工种){
          requireError.push(`第${index + 1}行的工种名称未填写`);
        }
        if (!item.点位) {
          requireError.push(`第${index + 1}行的点位名称未填写`);
        }
        // 处理危害因素
        let harmFactors = (item.接触的职业病危害因素名称 || '').trim().replace(/，|;|；|、/g, ",");
        harmFactors = harmFactors.split(",");
        let errHarmFactors = [];
        for(let i = 0; i < harmFactors.length; i++){
          const name = harmFactors[i].trim();
          const checkFlag = await this.checkHarmFactor(name);
          if(!checkFlag){
            errHarmFactors.push(name);
          }
        }
        if(errHarmFactors.length > 0){
          requireError.push(`第${index + 1}行的危害因素名称"${errHarmFactors.join('、')}"不存在，请检查`);
        }

        // 处理接触时间
        let daytime = 0;
        let weektime = 0;
        if(item.接触时间){
          let touchtimes = item.接触时间.split("h/d");
          if (!touchtimes[1]) {
            return (touchTimeErr = "接触时间格式出错，正确格式如下：8h/d;5d/w");
          }
          daytime = touchtimes[0];
          daytime = daytime.match(/\d+(\.\d+)|\d+/g); // 匹配连续的数字包括小数点
          if (!daytime || !daytime[0])
            return (touchTimeErr = "接触时间格式出错，正确格式如下：8h/d;5d/w");
          daytime = daytime[daytime.length - 1];
          weektime = touchtimes[1].split("d/w");
          if (weektime.length !== 2)
            return (touchTimeErr = "接触时间格式出错，正确格式如下：8h/d;5d/w");
          weektime = weektime[0];
          weektime = weektime.match(/\d+(\.\d+)|\d+/g); // 匹配连续的数字包括小数点
          if (!weektime || !weektime[0])
            return (touchTimeErr = "接触时间格式出错，正确格式如下：8h/d;5d/w");
          weektime = weektime[weektime.length - 1];
        }
        // 返回数据
        return {
          workshopName: item.厂房 ? item.厂房.trim() : '',
          workspaceName: item.车间 ? item.车间.trim() : '',
          workTypeName: item.工种 ? item.工种.trim() : '',
          dailyProduce: item.班制 || '',
          exposedPeopleNumber: item['总接触人数（人）'],
          exposureHours: daytime,
          workDays: weektime,
          stationInfo: {
            stationName: item.点位 || '',
            harmFactors,
            protectiveFacilities: item.职业病防护设施及运行情况 ? item.职业病防护设施及运行情况.trim() : '',
            protectiveEquipment: item.个人防护用品发放及使用情况 ? item.个人防护用品发放及使用情况.trim() : '',
          },
        };
      });
      this.importData = await Promise.all(importData);
      requireError = Array.from(new Set(requireError));
      this.requireError = requireError;
      this.touchTimeErr = touchTimeErr;
      if (requireError.length > 0 || touchTimeErr) {
        this.dialogTip = true;
        return;
      } else {
        this.dialogTip = false;
      }
      this.showSaveLoad = true;
      this.percentage = 0;
      this.percentageItem = 0;
      this.ProgressLength = this.importData.length;
      this.ProgressRatio = parseFloat(100 / this.importData.length);
      // 如果 socket 实例不存在 就初始化一个，否则不做socket的初始化，直接发请求
      if (!this.socket) {
        this.openSocket();
      } else {
        this.handleAddMills();
      }
    },
    handleAddMills() {
      const socket = this.socket;
      socket.emit(
        "addWorkspaces",
        JSON.stringify(this.importData)
      );
    },
    async handleSuccess({ results, header }) {
      this.tableData = results;
      this.tableHeader = header;
      driver.isActivated && driver.moveNext();
    },
  },
};
</script>

<style scoped>
.submitBtn {
  margin: 30px auto;
  display: block;
}
</style>
