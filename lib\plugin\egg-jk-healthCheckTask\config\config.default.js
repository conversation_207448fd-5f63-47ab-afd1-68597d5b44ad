/* eslint valid-jsdoc: "off" */
exports.jk_Health_Check_Task = {
  alias: 'healthCheckTask', // 插件目录，必须为英文
  pkgName: 'egg-jk-healthCheckTask', // 插件包名
  enName: 'jk_Health_Check_Task', // 插件名
  name: '体检全流程', // 插件名称
  description: '体检流程', // 插件描述
  adminApi: [
    // {
    //   url: 'healthCheckTask/addHealthItem',
    //   method: 'post',
    //   controllerName: 'addHealthItem',
    //   details: '新增职业健康检查项目',
    // },
    {
      url: 'healthCheckTask/addNewHealthItem',
      method: 'post',
      controllerName: 'addNewHealthItem',
      details: '新增健康检查项目',
    },
    {
      url: 'healthCheckTask/getHealthItemList',
      method: 'get',
      controllerName: 'getHealthItemList',
      details: '获取健康检查项目列表',
    },
    {
      url: 'healthCheckTask/editHealthItem',
      method: 'post',
      controllerName: 'editHealthItem',
      details: '编辑健康检查信息',
    },
    {
      url: 'healthCheckTask/deleteHealthItem',
      method: 'post',
      controllerName: 'deleteHealthItem',
      details: '删除健康检查项目',
    },
    {
      url: 'healthCheckTask/newAddHealthCheckItem',
      method: 'post',
      controllerName: 'newAddHealthCheckItem',
      details: '新增职业健康检查项目',
    },
    {
      url: 'healthCheckTask/getHCItemList',
      method: 'get',
      controllerName: 'getHCItemList',
      details: '职业健康检查项目列表',
    },
    {
      url: 'healthCheckTask/getItemBasicInfo',
      method: 'get',
      controllerName: 'getItemBasicInfo',
      details: '职业健康检查项目详情',
    },
    {
      url: 'healthCheckTask/updateItemBasicInfo',
      method: 'post',
      controllerName: 'updateItemBasicInfo',
      details: '更新收费项目基础信息',
    },
    {
      url: 'healthCheckTask/newAddCheckDepartment',
      method: 'post',
      controllerName: 'newAddCheckDepartment',
      details: '新增科室',
    },
    {
      url: 'healthCheckTask/getCheckDepartmentList',
      method: 'get',
      controllerName: 'getCheckDepartmentList',
      details: '获取科室列表',
    },
    {
      url: 'healthCheckTask/addSmallHcProject',
      method: 'post',
      controllerName: 'addSmallHcProject',
      details: '更新体检项目中的检查项目',
    },
    {
      url: 'healthCheckTask/getNoChooseItem',
      method: 'get',
      controllerName: 'getNoChooseItem',
      details: '获取未选择检查项目列表',
    },
    {
      url: 'healthCheckTask/getHasChooseItem',
      method: 'get',
      controllerName: 'getHasChooseItem',
      details: '获取已选择检查项目列表',
    },
    {
      url: 'healthCheckTask/removeHasChooseItem',
      method: 'post',
      controllerName: 'removeHasChooseItem',
      details: '移除指定的检查项目',
    },
    {
      url: 'healthCheckTask/getInsitutionInfo',
      method: 'get',
      controllerName: 'getInsitutionInfo',
      details: '获取体检机构信息',
    },
    {
      url: 'healthCheckTask/updateInsitutionInfo',
      method: 'post',
      controllerName: 'updateInsitutionInfo',
      details: '更新机构信息',
    },
    // 自动判定委会
    {
      url: 'healthCheckTask/setHighLowTips',
      method: 'post',
      controllerName: 'setHighLowTips',
      details: '设置检查项目偏高偏低提示',
    },
    // 预约时间段相关路由
    {
      url: 'healthCheckAppointment/create',
      method: 'post',
      controllerName: 'createAppointmentTime',
      details: '创建预约时间段',
    },
    {
      url: 'healthCheckTask/getHcCompanyInfo',
      method: 'get',
      controllerName: 'getHcCompanyInfo',
      details: '搜索企业端已有公司详情',
    },
    {
      url: 'healthCheckAppointment/update',
      method: 'post',
      controllerName: 'updateAppointmentTime',
      details: '更新预约时间段',
    },
    {
      url: 'healthCheckAppointment/numbers',
      method: 'post',
      controllerName: 'updateAppointmentNumbers',
      details: '更新预约人数',
    },
    {
      url: 'healthCheckAppointment/list',
      method: 'post',
      controllerName: 'getAppointmentList',
      details: '获取预约列表',
    },
    {
      url: 'healthCheckAppointment/detail',
      method: 'get',
      controllerName: 'getAppointmentDetail',
      details: '获取预约详情',
    },
    {
      url: 'healthCheckAppointment/delete',
      method: 'get',
      controllerName: 'deleteAppointment',
      details: '删除预约',
    },
    // 计算类项目管理
    {
      url: 'computedItems/create',
      method: 'post',
      controllerName: 'createComputedItems',
      details: '创建计算类项目',
    },
    {
      url: 'computedItems/update',
      method: 'post',
      controllerName: 'updateComputedItems',
      details: '更新计算类项目',
    },
    {
      url: 'computedItems/removes',
      method: 'post',
      controllerName: 'removesComputedItems',
      details: '删除计算类项目',
    },
    {
      url: 'computedItems/list',
      method: 'post',
      controllerName: 'listComputedItems',
      details: '获取计算类项目列表',
    },
    {
      url: 'computedItems/getOne',
      method: 'get',
      controllerName: 'getOneComputedItems',
      details: '获取计算类项目详情',
    },
    // 
    {
      url: 'healthCheckTask/addMatainCompany',
      method: 'post',
      controllerName: 'addMatainCompany',
      details: '新增维护公司',
    },
    {
      url: 'healthCheckTask/getMatainCompanyList',
      method: 'get',
      controllerName: 'getMatainCompanyList',
      details: '获取维护公司列表',
    },
    {
      url: 'healthCheckTask/updateMatainCompanyInfo',
      method: 'post',
      controllerName: 'updateMatainCompanyInfo',
      details: '更新维护公司数据',
    },
    {
      url: 'healthCheckTask/getMatainCompanyDetail',
      method: 'get',
      controllerName: 'getMatainCompanyDetail',
      details: '获取维护公司详情',
    },
    {
      url: 'healthCheckTask/getReservationPeopleData',
      method: 'get',
      controllerName: 'getReservationPeopleData',
      details: '获取预约人员列表',
    },
    // 体检登记相关路由
    {
      url: 'healthCheckRegister/findHarmFactors',
      method: 'get',
      controllerName: 'findHarmFactors',
      details: '获取危害因素',
    },
    {
      url: 'healthCheckRegister/list',
      method: 'post',
      controllerName: 'getRegisterList',
      details: '获取体检登记列表',
    },
    {
      url: 'healthCheckRegister/getByCheckNo',
      method: 'get',
      controllerName: 'getRegisterDetailByCheckNo',
      details: '通过体检编号获取体检登记信息',
    },
    {
      url: 'healthCheckRegister/getByIdNumber',
      method: 'get',
      controllerName: 'getRegisterDetailByIdNumber',
      details: '通过证件号获取体检登记信息',
    },
    {
      url: 'healthCheckRegister/update',
      method: 'post',
      controllerName: 'updateRegister',
      details: '更新体检登记',
    },
    {
      url: 'healthCheckRegister/detail',
      method: 'get',
      controllerName: 'getRegisterDetail',
      details: '获取体检登记详情',
    },
    {
      url: 'healthCheckRegister/occupationalDiseaseList',
      method: 'get',
      controllerName: 'occupationalDiseaseList',
      details: '获取职业病列表',
    },
    {
      url: 'healthCheckRegister/occupationalContraindicationList',
      method: 'get',
      controllerName: 'occupationalContraindicationList',
      details: '获取职业禁忌证列表',
    },
    // getDepartmentAndProjectList
    {
      url: 'healthCheckRegister/getDepartmentAndProjectList',
      method: 'get',
      controllerName: 'getDepartmentAndProjectList',
      details: '获取科室和收费项目列表',
    },
    {
      url: 'healthCheckRegister/create',
      method: 'post',
      controllerName: 'createRegister',
      details: '创建体检登记',
    },
    {
      url: 'healthCheckRegister/getTodayRegisterList',
      method: 'get',
      controllerName: 'getTodayRegisterList',
      details: '获取当日登记人员列表',
    },
    {
      url: 'healthCheckRegister/inputResult',
      method: 'post',
      controllerName: 'inputResult',
      details: '结果录入',
    },
    {
      url: 'healthCheckRegister/departmentSummary',
      method: 'post',
      controllerName: 'departmentSummary',
      details: '科室小结',
    },
    {
      url: 'healthCheckRegister/healthCheckSummary',
      method: 'post',
      controllerName: 'healthCheckSummary',
      details: '检查总结',
    },
    {
      url: 'healthCheckRegister/getHCReportList',
      method: 'post',
      controllerName: 'getHCReportList',
      details: '获取体检报告列表',
    },
    {
      url: 'healthCheckRegister/getHCReportDetail',
      method: 'get',
      controllerName: 'getHCReportDetail',
      details: '获取体检报告详情',
    },
    {
      url: 'healthCheckRegister/reportReview',
      method: 'post',
      controllerName: 'reportReview',
      details: '报告审核',
    },
    // {
    //   url: 'healthCheckRegister/delete',
    //   method: 'get',
    //   controllerName: 'deleteRegister',
    //   details: '删除体检登记',
    // },
    {
      url: 'healthCheckRegister/detail',
      method: 'get',
      controllerName: 'getRegisterDetail',
      details: '获取体检登记详情',
    },
    {
      url: 'healthCheckTask/getDepartmentDetail',
      method: 'get',
      controllerName: 'getDepartmentDetail',
      details: '获取科室基本数据',
    },
    {
      url: 'healthCheckTask/updateDepartmentInfo',
      method: 'post',
      controllerName: 'updateDepartmentInfo',
      details: '更新科室基本数据',
    },
    {
      url: 'healthCheckFeeManages/getFeesList',
      method: 'post',
      controllerName: 'getFeesList',
      details: '获取费用列表',
    },
    {
      url: 'healthCheckFeeManages/getPersonListByContractId',
      method: 'get',
      controllerName: 'getPersonListByContractId',
      details: '根据合同ID查询本合同下已登记体检的人员',
    },
    {
      url: 'healthCheckFeeManages/getFeeDetailByContractId',
      method: 'get',
      controllerName: 'getFeeDetailByContractId',
      details: '根据合同ID查询本合同下已体检的费用明细',
    },
    {
      url: 'healthCheckFeeManages/getContractFile',
      method: 'get',
      controllerName: 'getContractFile',
      details: '获取合同附件',
    },
    {
      url: 'healthCheckTask/getSuspectDiseaseList',
      method: 'get',
      controllerName: 'getSuspectDiseaseList',
      details: '获取疑似职业病上报列表',
    },
    {
      url: 'healthCheckTask/reportSuspectDisease',
      method: 'post',
      controllerName: 'reportSuspectDisease',
      details: '疑似职业病上报',
    },
    {
      url: 'healthCheckTask/getAppointedList',
      method: 'post',
      controllerName: 'getAppointedList',
      details: '获取已预约负荷列表',
    },
    {
      url: 'healthCheckTask/getTjExceptionList',
      method: 'get',
      controllerName: 'getTjExceptionList',
      details: '获取体检异常列表(非正常)',
    },
    {
      url: 'healthCheckTask/getNoHealthCheckList',
      method: 'get',
      controllerName: 'getNoHealthCheckList',
      details: '获取预约后未体检人员列表',
    },
    {
      url: 'healthCheckTask/statisticExamTypeTjCountByYear',
      method: 'get',
      controllerName: 'statisticExamTypeTjCountByYear',
      details: '按年度统计上岗在岗离岗体检人数',
    },
    {
      url: 'healthCheckTask/statisticExamTypeTjCountByYearFactor',
      method: 'get',
      controllerName: 'statisticExamTypeTjCountByYearFactor',
      details: '按年度统计每种危害因素上岗在岗离岗体检人数',
    },
    {
      url: 'healthCheckTask/tjCountChangeTrendy',
      method: 'get',
      controllerName: 'tjCountChangeTrendy',
      details: '历年体检人数变化情况',
    },
    {
      url: 'healthCheckTask/tjCountChangeTrendyBaseYearFactor',
      method: 'get',
      controllerName: 'tjCountChangeTrendyBaseYearFactor',
      details: '历年每种危害因素的上岗/在岗/离岗体检人数变化情况以及体检正常和异常人数变化情况',
    },

    // #region 统计
    {
      url: 'healthCheckRegister/getStatisticsByYear',
      method: 'get',
      controllerName: 'getStatisticsByYear',
      details: '按照年度统计',
    },
    {
      url: 'healthCheckRegister/getStatisticsByYearAndHarmFactor',
      method: 'get',
      controllerName: 'getStatisticsByYearAndHarmFactor',
      details: '按照年度统计每种危害因素',
    },
    {
      url: 'healthCheckRegister/findHarmFactors2',
      method: 'get',
      controllerName: 'findHarmFactors2',
      details: '查询危害因素',
    },
    // #endregion
  ],
  fontApi: [
    // /api/healthCheckTask
    {
      url: 'healthCheckTask/getResultTypeOption',
      method: 'get',
      controllerName: 'getResultTypeOption',
      details: '获取结果类型选项',
    },
    {
      url: 'healthCheckTask/getHealthCheckUnit',
      method: 'get',
      controllerName: 'getHealthCheckUnit',
      details: '获取单位选项',
    }
  ],

  initData: '', // 初始化数据脚本
  pluginsConfig: `
    exports.jk_Health_Check_Task = {\n
        enable: true,\n        package: 'egg-jk-healthCheckTask',
    };\n
    `, // 插入到 plugins.js 中的配置
  defaultConfig: `
    healthCheckTaskRouter:{\n
      match: [ctx => ctx.path.startsWith('/manage/healthCheckTask'), ctx => ctx.path.startsWith('/api/healthCheckTask')],\n
    },\n
    `,
  // 插入到 config.default.js 中的配置
  security: {
    csrf: {
      ignoreJSON: true,
    },
    // 白名单
    // domainWhiteList: ['*'],
  },
};

