
module.exports = app => {
  const shortid = require('shortid');
  // 员工信息表
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const dingtree = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    EnterpriseID: String,
    name: String, // 部门名称
    id: String, // 钉钉生成的id
    parentid: String, // 父级id
    type: String,
    sortId: {
      type: Number,
      default: 0,
    },
    staff: [{ type: String, ref: 'Employees' }], // 员工
    dingId: String,
  });
  return mongoose.model('Dingtrees', dingtree, 'dingtrees');
};
