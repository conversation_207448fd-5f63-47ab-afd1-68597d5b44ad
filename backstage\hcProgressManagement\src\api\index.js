import request from '@root/publicMethods/request';

//  获取维护公司列表
export function getMatainCompanyList(params) {
  return request({
    url: '/manage/healthCheckTask/getMatainCompanyList',
    params,
    method: 'get',
  });
}

// 获取预约列表
export function createRegister(data) {
  return request({
    url: '/manage/healthCheckRegister/create',
    data,
    method: 'post',
  });
}
// 获取预约列表
export function getRegisterList(data) {
  return request({
    url: '/manage/healthCheckRegister/list',
    data,
    method: 'post',
  });
}

// 获取预约详情
export function getRegisterDetail(params) {
  return request({
    url: '/manage/healthCheckRegister/detail',
    params,
    method: 'get',
  });
}

// 更新预约
export function updateRegister(data) {
  return request({
    url: '/manage/healthCheckRegister/update',
    data,
    method: 'post',
  });
}

export function getRegisterDetailByCheckNo(params) {
  return request({
    url: '/manage/healthCheckRegister/getByCheckNo',
    params,
    method: 'get',
  });
}
export function getRegisterDetailByIdNumber(params) {
  return request({
    url: '/manage/healthCheckRegister/getByIdNumber',
    params,
    method: 'get',
  });
}

// 获取科室和收费项目列表
export function getDepartmentAndChargeItemsList(params) {
  return request({
    url: '/manage/healthCheckRegister/getDepartmentAndProjectList',
    params,
    method: 'get',
  });
}

// 获取当日登记人员列表
export function getTodayRegisterList(params) {
  return request({
    url: '/manage/healthCheckRegister/getTodayRegisterList',
    params,
    method: 'get',
  });
}

// 结果录入
export function inputResult(data) {
  return request({
    url: '/manage/healthCheckRegister/inputResult',
    data,
    method: 'post',
  });
}

// 科室小结
export function departmentSummary(data) {
  return request({
    url: '/manage/healthCheckRegister/departmentSummary',
    data,
    method: 'post',
  });
}

// 检查总结
export function healthCheckSummary(data) {
  return request({
    url: '/manage/healthCheckRegister/healthCheckSummary',
    data,
    method: 'post',
  });
}

// 查找危害因素
export function findHarmFactors(params) {
  return request({
    url: '/manage/healthCheckRegister/findHarmFactors',
    method: 'get',
    params,
  });
}

// 获取职业病列表healthCheckRegister/occupationalDiseaseList
export function occupationalDiseaseList(params) {
  return request({
    url: '/manage/healthCheckRegister/occupationalDiseaseList',
    method: 'get',
    params,
  });
}

// 获取职业禁忌证列表healthCheckRegister/occupationalContraindicationList
export function occupationalContraindicationList(params) {
  return request({
    url: '/manage/healthCheckRegister/occupationalContraindicationList',
    method: 'get',
    params,
  });
}