<template>
  <div class="serveEnterprise">
    <TitleTag titleName="查询条件"></TitleTag>
    <div class="search-container">
      <el-form :inline="true" :model="formSearch" class="demo-form-inline">
        <el-form-item label="姓名">
          <el-input v-model="formSearch.name" placeholder="请输入姓名"></el-input>
        </el-form-item>
        <el-form-item label="预约状态">
          <el-select v-model="formSearch.reservationStatu" placeholder="请选择">
            <el-option v-for="item in reservationOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="体检类别">
          <el-select v-model="formSearch.examType" placeholder="请选择">
            <el-option label="离岗" :value="0"></el-option>
            <el-option label="岗前" :value="1"></el-option>
            <el-option label="在岗" :value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit" icon="el-icon-search">查询</el-button>
        </el-form-item>
        <el-form-item>
          <el-button @click="reset" icon="el-icon-refresh">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <TitleTag titleName="预约记录列表">
      <el-button type="primary" size="mini" plain @click="batchReason('批量同意')">批量同意</el-button>
      <el-button type="danger" size="mini" plain @click="batchReason('批量拒绝')">批量拒绝</el-button>
    </TitleTag>
    <div class="table">
      <el-table :data="tableData" row-key="_id" tooltip-effect="light" style="width: 100%" stripe border
        header-cell-style="background-color: #f5f7fa; color: #606266;height:46px" @selection-change="selectionChange">
        <!-- 已同意或已拒绝的不能被选中 -->
        <el-table-column type="selection" align="center" width="40" :selectable="isRowSelectable"></el-table-column>
        <el-table-column type="index" width="50" label="序号" align="center"></el-table-column>
        <el-table-column label="姓名" align="center" prop="name" show-overflow-tooltip></el-table-column>
        <el-table-column label="体检类别" align="center">
          <template slot-scope="scope">
            <el-tag type="primary" size="mini" v-if="scope.row.examType === 0">离岗</el-tag>
            <el-tag type="warning" size="mini" v-if="scope.row.examType === 2">在岗</el-tag>
            <el-tag type="success" size="mini" v-if="scope.row.examType === 1">岗前</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="预约日期" align="center" prop="reservationDateFormat"></el-table-column>
        <el-table-column label="预约状态" align="center">
          <template slot-scope="scope">
            <!-- <el-tag type="primary" size="mini" v-if="scope.row.reservationStatus === 0">未预约</el-tag> -->
            <el-tag type="warning" size="mini" v-if="scope.row.reservationStatu === 1">已预约</el-tag>
            <el-tag type="success" size="mini" v-if="scope.row.reservationStatu === 2">已同意</el-tag>
            <el-tag type="danger" size="mini" v-if="scope.row.reservationStatu === 3">已拒绝</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="体检编号" align="center" prop="registerCheckNo"></el-table-column>
        <el-table-column label="登记状态" align="center">
          <template slot-scope="scope">
            <!--  0 未登记 1 已登记 2 已总结(待审核) 3 审核通过 4 审核不通过 -->
            <el-tag type="info" size="mini" v-if="scope.row.registerStatus === 0">未登记</el-tag>
            <el-tag type="primary" size="mini" v-if="scope.row.registerStatus === 1">已登记</el-tag>
            <el-tag type="warning" size="mini" v-if="scope.row.registerStatus === 2">已总结(待审核)</el-tag>
            <el-tag type="success" size="mini" v-if="scope.row.registerStatus === 3">审核通过</el-tag>
            <el-tag type="danger" size="mini" v-if="scope.row.registerStatus === 4">审核不通过</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="120" fixed="right">
          <template slot-scope="scope">
            <el-button v-if="scope.row.reservationStatu === 1" type="text"
              @click="agreeReservation(scope.row._id, scope.row.name)">同意</el-button>
            <el-button v-if="scope.row.reservationStatu === 1" type="text"
              @click="openRefuse(scope.row._id, scope.row.name)" style="color:#F56C6C;">拒绝</el-button>
            <el-button v-if="scope.row.reservationStatu === 2 && scope.row.registerStatus === 0"
              type="text" @click="go('/admin/hcProgressManagement/hcRegisterEdit', scope.row)">前往登记</el-button>
            <el-button v-if="scope.row.reservationStatu === 2 && scope.row.registerStatus > 0"
              type="text" @click="go('/admin/hcProgressManagement/hcRegisterDetail', scope.row)">查看登记内容</el-button>
            <!-- <el-button v-if="scope.row.registerStatus === 1" type="text" @click="getDetail(scope.row)" style="color:#409EFF;">查看详情</el-button> -->
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination">
      <el-pagination @size-change="getReservationPeopleDataList" @current-change="getReservationPeopleDataList"
        :current-page.sync="pageInfo.pageNum" :page-size.sync="pageInfo.pageSize" :page-sizes="[10, 20, 30, 50, 100]"
        background layout="total, sizes, prev, pager, next, jumper" :total="total">
      </el-pagination>
    </div>
    <el-dialog :visible.sync="reasonDialog" title="拒绝原因" @close="closeDialog">
      <div style="margin-bottom: 15px;">拒绝原因：</div>
      <el-input placeholder="请输入拒绝原因" v-model="refuseReason" type="textarea" show-word-limit maxlength="200" autosize />
      <template #footer>
        <el-button type="primary" @click="refuseReservation">确定</el-button>
        <el-button @click="closeDialog">取消</el-button>
      </template>
    </el-dialog>
    <SuspectDiseaseDetail :show.sync="isShowDetail" :itemData="currentData" />
  </div>
</template>

<script>
import moment from 'moment'
import SuspectDiseaseDetail from '../components/SuspectDiseaseDetail.vue';
import TitleTag from '../components/TitleTag.vue';
import { getReservationPeopleData, medicalExamBook } from '@/api'
export default {
  components: {
    SuspectDiseaseDetail,
    TitleTag
  },
  data() {
    return {
      total: null,
      pageInfo: {
        pageNum: 1,
        pageSize: 10
      },
      selectedIds: [], // 选中的id
      reservationOptions: [
        { label: '已预约', value: 1 },
        { label: '已同意', value: 2 },
        { label: '已拒绝', value: 3 }
      ],
      tableData: [],
      isShowDetail: false,
      reasonDialog: false,    // 控制拒绝弹窗
      refuseReason: null,     // 拒绝原因
      currentData: null,
      formSearch: {
        name: '',
        reservationStatu: null,
        examType: null
      }
    }
  },
  created() {
    this.getReservationPeopleDataList()
  },
  methods: {
    // 选中行时触发
    selectionChange(arr) {
      if (arr.length === 0) {
        this.selectedIds = null
      } else {
        this.selectedIds = arr.map(item => item._id)
      }
    },
    // 仅允许预约状态为1（已预约）的行可被选中
    isRowSelectable(row) {
      return row.reservationStatu == 1;
    },
    // 批量处理
    async batchReason(title) {
      if (!this.selectedIds) {
        return this.$message({ type: 'warning', message: '至少选择一项数据' })
      } else {
        const name = `所选${this.selectedIds.length}位劳动者`
        if (title === '批量拒绝') {
          this.openRefuse(this.selectedIds, name)
        } else {
          this.agreeReservation(this.selectedIds, name)
        }
      }
    },

    // 同意预约
    async agreeReservation(id, name) {
      this.$confirm(`确认同意${name}的预约申请?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        let params = {
          id,
          reservationStatu: 2
        }
        const res = await medicalExamBook(params)
        if (res.status === 200) {
          this.reasonDialog = false
          this.getReservationPeopleDataList()
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
        } else {
          this.$message.error(res.message)
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    },
    openRefuse(id, name) {
      this.currentId = id
      this.currentName = name
      this.reasonDialog = true
    },
    // 拒绝预约
    async refuseReservation() {
      if (!this.refuseReason) {
        this.$message.warning('请填写拒绝原因。')
        return
      }
      this.$confirm(`确认拒绝${this.currentName}的预约申请嘛?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        let params = {
          id: this.currentId,
          reservationStatu: 3
        }
        await medicalExamBook(params)
        this.reasonDialog = false
        this.getReservationPeopleDataList()
        this.$message({
          type: 'success',
          message: '操作成功!'
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    },
    closeDialog() {
      this.refuseReason = this.$options.data()['refuseReason']
      this.reasonDialog = false
    },
    async getReservationPeopleDataList() {
      let params = {
        ...this.pageInfo,
        ...this.formSearch
      }
      const res = await getReservationPeopleData(params)
      this.tableData = this.handleData(res.data.res)
      this.total = res.data.total
    },
    handleData(val) {
      val.forEach(item => {
        item.reservationDateFormat = moment(item.reservationDate).format('YYYY-MM-DD')
        item.registerStatus = item.healthCheckRegister ? item.healthCheckRegister.status : undefined
      })
      return val
    },
    // 预约体检
    toReservation(row) {
      this.tableData.forEach(item => {
        if (row.id === item.id) {
          item.reservationStatus = 1
        }
      })
    },
    // 查看预约详情
    go(path, row) {
      const _id = row.healthCheckRegister._id
      this.$router.push({
        path,
        query: { _id }
      })

    },
    // 取消预约
    cancelReservation(row) {
      this.tableData.forEach(item => {
        if (row.id === item.id) {
          item.reservationStatus = 0
        }
      })
    },
    // 重新预约
    reReservation(row) {
      this.tableData.forEach(item => {
        if (row.id === item.id) {
          item.reservationStatus = 1
        }
      })
    },
    toReservation() {
      this.isShowDetail = true
    },
    getDetail(row) {
      this.currentData = row
      this.isShowDetail = true
    },
    async getList() {

    },
    onSubmit() {
      this.getReservationPeopleDataList()
    },
    reset() {
      this.formSearch = this.$options.data()['formSearch']
      this.pageInfo = this.$options.data()['tableData']
      this.getReservationPeopleDataList()
    }
  },
  filters: {
    handletoPhone(val) {
      let reg = /^(1[3-9][0-9])\d{4}(\d{4}$)/;
      let phone = val.replace(reg, '$1****$2');
      return phone;
    },
    handletoIdNumber(val) {
      const reg = /^(\d{6})\d{8}(\d{4})$/;
      return val.replace(reg, "$1********$2");
    }
  }
}
</script>

<style lang="scss" scoped>
.serveEnterprise {
  margin-left: 210px;
  padding: 15px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  // margin-top: 15px;
}

.num {
  font-size: 18px;
  color: #1691e0;
  font-weight: bolder;
}

.table {
  margin-top: 10px;
}

.pagination {
  margin-top: 10px;
  display: flex;
  justify-content: center;
}

::v-deep .el-table__fixed,
::v-deep .el-table__fixed-right {
  height: 100% !important;
}

.excel-upload-input {
  display: none;
  z-index: -9999;
}
</style>