const Service = require('egg').Service;
const path = require('path');
const { nanoid } = require('nanoid');
const fs = require('fs');
const { mkdirp } = require('mkdirp');
const sendToWormhole = require('stream-wormhole');
const awaitWriteStream = require('await-stream-ready').write;
const moment = require('moment');

class ContractTableService extends Service {
  // 新增科室
  async newAddCheckDepartment(query) {
    const { ctx } = this;
    query.createDate = new Date()
    await ctx.model.HealthDepartment.create(query);
  }

  // 获取符合条件的检查项目的全部列表
  async getAllItemList(query) {
    const { ctx } = this;
    const filter = {};
    if (query.projectName) {
      filter.projectName = { $regex: query.projectName, $options: 'i' };
    }
    if (query.projectNumber) {
      filter.projectNumber = { $regex: query.projectNumber, $options: 'i' };
    }
    filter.physicalOrgId = query.physicalOrgId
    const res = await ctx.model.HealthCheckItemInfo.find(filter)
    return res
  }

  // 获取该机构对应的体检项目的检查项目
  async getAllHasItemList(query) {
    const { ctx } = this;
    const filter = {};
    filter.physicalOrgId = query.physicalOrgId
    filter._id = query.id
    const res = await ctx.model.HealthCheckProject.findOne(filter)
    return res
  }

  // 移除检查项目
  async removeHasChooseItem(query) {
    const { ctx } = this;
    const filter = {};
    filter.physicalOrgId = query.physicalOrgId
    filter._id = query.id
    const res = await ctx.model.HealthCheckProject.findOne(filter)     // 获取对应数据
    let itemArray = res.includeProject
    itemArray = itemArray.filter(item => {
      return item._id !== query.itemId
    })
    await ctx.model.HealthCheckProject.updateOne(
      { _id: query.id, physicalOrgId: query.physicalOrgId },
      { $set: { includeProject: itemArray } },
      { upsert: true }
    );
  }



  // 获取科室列表
  async getCheckDepartmentList(query) {
    const pageNum = Number(query.pageNum);
    const pageSize = Number(query.pageSize);
    const { ctx } = this;
    const filter = {};
    if (query.physicalOrgId) {
      filter.physicalOrgId = query.physicalOrgId;
    }
    if (query.departmentName) {
      filter.departmentName = { $regex: query.departmentName, $options: 'i' };
    }
    // if (query.projectNumber) {
    //   filter.projectNumber = { $regex: query.projectNumber, $options: 'i' };
    // }
    if (query.departmentType) {
      filter.departmentType = query.departmentType;
    }
    const res = await ctx.model.HealthDepartment.find(filter)
      .skip((pageNum - 1) * pageSize)
      .limit(pageSize)
      .sort({ createTime: -1 });
    // 统计总数
    const sum = await ctx.model.HealthDepartment.find(filter)
    console.log(sum, '========123')
    // const total = sum.filter(item => !item._id)
    return {
      res,
      total: sum.length
    }
  }

  // 新增检查项目
  async addNewHealthItem(query) {
    const { ctx } = this;
    await ctx.model.HealthCheckItemInfo.create(query);
  }

  async newAddHealthCheckItem(query) {
    const { ctx } = this;
    await ctx.model.HealthCheckProject.create(query);
  }

  async getHealthItemList(query) {
    const pageNum = Number(query.pageNum);
    const pageSize = Number(query.pageSize);
    const { ctx } = this;
    const filter = { useStatus: { $ne: 2 } }   // 过滤掉已经删除的检查项目
    if (query.physicalOrgId) {
      filter.physicalOrgId = query.physicalOrgId;
    }
    if (query.projectName) {
      filter.projectName = { $regex: query.projectName, $options: 'i' };
    }
    if (query.projectNumber) {
      filter.projectNumber = { $regex: query.projectNumber, $options: 'i' };
    }

    if (query.marriage) {
      filter.marriage = query.marriage;
    }
    if (query.suitType) {
      filter.suitType = { $in: Number(query.suitType) }
    }
    const res = await ctx.model.HealthCheckItemInfo.find(filter)
      .skip((pageNum - 1) * pageSize)
      .limit(pageSize)
      .sort({ createdAt: -1 });
    // 统计总数
    const sum = await ctx.model.HealthCheckItemInfo.find(filter)
    const total = sum.filter(item => item._id !== null).length;
    return {
      res,
      total
    }
  }

  async getHCItemList(query) {
    const pageNum = Number(query.pageNum);
    const pageSize = Number(query.pageSize);
    const { ctx } = this;
    const filter = {}
    if (query.itemType) {
      filter.itemType = query.itemType;
    }
    if (query.physicalOrgId) {
      filter.physicalOrgId = query.physicalOrgId;
    }
    if (query.itemName) {
      filter.itemName = { $regex: query.itemName, $options: 'i' };
    }

    const res = await ctx.model.HealthCheckProject.find(filter)
      .skip((pageNum - 1) * pageSize)
      .limit(pageSize)
      .sort({ createTime: -1 });
    const sum = await await ctx.model.HealthCheckProject.find(filter)
    const total = sum.filter(item => item._id !== null).length;
    return {
      res,
      total
    }
  }

  escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  // 查询qy端用人单位信息(模糊查询)
  async getHcCompanyInfo(query) {
    const { ctx } = this;
    const searchCondition = {};
    if (query.cname) {
      const cname = this.escapeRegExp(query.cname);
      searchCondition.cname = { $regex: cname, $options: 'i' };
    }
    if (query.code) {
      const code = this.escapeRegExp(query.code);
      searchCondition.code = { $regex: code, $options: 'i' };
    }
    const res = await ctx.model.Adminorg.find(searchCondition).limit(5)
    return res;
  }

  async getInsitutionInfo(query) {
    try {
      const { ctx } = this;
      const searchCondition = {};
      if (query.id) {
        searchCondition._id = { _id: query.id };
      }
      const res = await ctx.model.PhysicalExamOrg.findOne(searchCondition)
      return res
    } catch (error) {
      // 错误捕获
    }
  }

  // 编辑健康检查信息
  async editHealthItem(query) {
    const { ctx } = this;
    let updateFields = {}

    query.updateTime = new Date()
    for (const key in query) {
      if (query.hasOwnProperty(key) && key !== '_id') { // 排除不需要更新的字段，比如 id
        updateFields[key] = query[key];
      }
    }
    await ctx.model.HealthCheckItemInfo.updateOne(
      { _id: query._id }, // 查询条件
      { $set: updateFields } // 动态更新的字段
    )
  }

  async setHighLowTips(params) {
    const { ctx } = this;
    const { highValueTips, lowValueTips, _id } = params
    await ctx.model.HealthCheckItemInfo.updateOne(
      { _id: _id },
      {
        $set: {
          highValueTips,
          lowValueTips
        }
      }
    )
  }

  // 审核体检合同
  async auditContract(query) {
    const { ctx } = this;
    await ctx.model.MedicalExamContract.updateOne({ _id: query.id }, { $set: { auditStatu: query.auditStatu } });
    if (query.auditStatu === 2) {
      const info = await ctx.model.MedicalExamContract.find({ _id: query.id }).select('cId contractType');
      const type = info[0].contractType;
      const id = info[0].cId;
      const employees = await ctx.model.Employee.find({ EnterpriseID: id }).select('name phoneNum IDNum');
      const planData = employees.map(item => {
        return {
          idNumber: item.IDNum,
          name: item.name,
          contactPhone: item.phoneNum ? item.phoneNum : '',
          reservationStatu: 0,
          examType: type,
          contractId: query.id,
          refuseReason: '',
          EnterpriseID: id,
          employeeId: item._id,
        };
      });
      await ctx.model.MedicalExamPlan.create(planData);
    }
  }

  // 上传体检合同
  async uploadContract(query) {
    const { ctx } = this;
    const stream = await ctx.getFileStream();
    const uploadPath = path.join(ctx.app.config.upload_path, query.physicalOrgId);
    const extname = path.extname(stream.filename).toLowerCase(); // 文件扩展名称
    const fileName = path.basename(stream.filename, extname); // 文件名称
    const uploadFileName = fileName + `_${nanoid(10)}` + extname;
    if (!fs.existsSync(uploadPath)) {
      mkdirp.sync(uploadPath);
    }
    const target = path.join(uploadPath, `${uploadFileName}`);
    const writeStream = fs.createWriteStream(target);
    try {
      await awaitWriteStream(stream.pipe(writeStream));
    } catch (err) {
      // 必须将上传的文件流消费掉，要不然浏览器响应会卡死
      await sendToWormhole(stream);
      throw err;
    }
    return {
      uploadFileName,
      target,
    };
  }

  // 更新体检项目
  async addSmallHcProject(query) {
    const { ctx } = this;
    await ctx.model.HealthCheckProject.updateOne(
      { _id: query.id, physicalOrgId: query.physicalOrgId },
      { $set: { includeProject: query.includeProject } },
      { upsert: true }
    );
  }

  // 体检预约个人详情
  async getPersonDetail(query) {
    const { ctx } = this;
    const pageNum = Number(query.pageNum);
    const pageSize = Number(query.pageSize);
    const filter = {};
    if (query.contractId) {
      filter.contractId = query.contractId;
    }
    if (query.reservationStatu) {
      filter.reservationStatu = query.reservationStatu;
    }
    if (query.examType) {
      filter.examType = query.examType;
    }
    if (query.name) {
      filter.name = { $regex: query.name, $options: 'i' };
    }
    if (query.contactPhone) {
      filter.contactPhone = { $regex: query.contactPhone, $options: 'i' };
    }
    if (query.idNumber) {
      filter.idNumber = { $regex: query.idNumber, $options: 'i' };
    }
    filter.reservationStatu = { $ne: 0 };
    const res = await ctx.model.MedicalExamPlan.find(filter)
      .select('name contactPhone idNumber contractType reservationStatu refuseReason reservationDate')
      .skip((pageNum - 1) * pageSize)
      .limit(pageSize);

    // 统计总数
    const sum = await await ctx.model.MedicalExamPlan.find(filter).select('contractId idNumber name')
    const total = sum.filter(item => item.contractId !== null).length;

    return {
      res,
      total
    }
  }

  // 获取体检机构详情
  async getPhysicalDetail(query) {
    try {
      const { ctx } = this;
      const searchCondition = {};
      if (query.id) {
        searchCondition._id = { _id: query.id };
      }
      const res = await ctx.model.PhysicalExamOrg.findOne(searchCondition).select('name organization contract phoneNum');
      return res
    } catch (error) {
      // 错误捕获
    }
  }

  // 获取体检项目详情
  async getItemBasicInfo(query) {
    try {
      const { ctx } = this;
      const searchCondition = {};
      if (query.id) {
        searchCondition._id = { _id: query.id };
      }
      const res = await ctx.model.HealthCheckProject.findOne(searchCondition)
      return res
    } catch (error) {
      // 错误捕获
    }
  }

  // 更新检查项目基础信息
  async updateItemBasicInfo(query) {
    try {
      const { ctx } = this
      const find = await ctx.model.HealthCheckProject.findOne({ _id: query._id });
      if (!find) {
        throw new Error('该项目不存在');
      }
      await ctx.model.HealthCheckProject.updateOne(
        { _id: query._id },
        { $set: { ...query } },
        // { upsert: true }
      );
    } catch (error) {
      console.log(error)
    }
  }

  // 更新体检机构数据
  async updateInsitutionInfo(query) {
    try {
      const { ctx } = this
      await ctx.model.PhysicalExamOrg.updateOne(
        { _id: query.physicalId },
        { $set: { ...query } },
        { upsert: true }
      );
    } catch (error) {
      console.log(error)
    }
  }

  // 新增体检维护公司
  async addMatainCompany(query) {
    const { ctx } = this;
    const { cname, code, EnterpriseID, physicalOrgId } = query;
    if (!cname) {
      throw new Error('用人单位名称不能为空');
    }
    if (!code) {
      throw new Error('统一社会信用代码不能为空');
    }
    if (!EnterpriseID) {
      throw new Error('必须为已注册的用人单位');
    }
    const find = await ctx.model.MatainCompany.findOne({ physicalOrgId, EnterpriseID });
    if (find) {
      throw new Error('该用人单位已存在，请勿重复添加');
    }
    await ctx.model.MatainCompany.create(query);
    return '新增成功';
  }

  // 获取维护公司列表
  async getMatainCompanyList(query) {
    try {
      const pageNum = Number(query.pageNum);
      const pageSize = Number(query.pageSize);
      const { ctx } = this;
      const filter = {};
      if (query.physicalOrgId) {
        filter.physicalOrgId = query.physicalOrgId;
      }
      if (query.cname) {
        filter.cname = { $regex: query.cname, $options: 'i' };
      }
      if (query.code) {
        filter.code = { $regex: query.code, $options: 'i' };
      }
      console.log(query, '======1234')
      const res = await ctx.model.MatainCompany.find(filter)
        .skip((pageNum - 1) * pageSize)
        .limit(pageSize)
      // .sort({ : -1 });
      // 统计总数
      const sum = await await ctx.model.MatainCompany.find(filter)
      const total = sum.filter(item => item._id !== null).length;
      return {
        res,
        total
      }
    } catch (error) {
      console.log(error)
    }
  }

  async getMatainCompanyDetail(query) {
    try {
      const { ctx } = this;
      const searchCondition = {};
      if (query.id) {
        searchCondition._id = { _id: query.id };
      }
      const res = await ctx.model.MatainCompany.findOne(searchCondition)
      return res
    } catch (error) {
      // 错误捕获
    }
  }

  async updateMatainCompanyInfo(query) {
    try {
      const { ctx } = this
      await ctx.model.MatainCompany.updateOne(
        { _id: query._id },
        { $set: { ...query } },
        // { upsert: true }
      );
    } catch (error) {
      console.log(error)
    }
  }

  async getReservationPeopleData(query) {
    try {
      const { ctx } = this;
      const pageNum = Number(query.pageNum || 1);
      const pageSize = Number(query.pageSize || 10);
      const filter = {};

      // 添加过滤条件
      // if (query.physicalOrgId) {
      //   filter.physicalOrgId = query.physicalOrgId;
      // }
      if (query.reservationStatu) {
        filter.reservationStatu = query.reservationStatu;
      } else {
        // 如果 query.reservationStatu 未传值，则默认添加 reservationStatu 不等于 0 的条件
        filter.reservationStatu = { $ne: 0 };
      }
      if (query.name) {
        filter.name = { $regex: query.name, $options: 'i' };
      }
      if (query.examType) {
        filter.examType = query.examType;
      }

      // 根据 physicalOrgId 查找 MedicalExamContract 的 _id 列表
      const match = { physicalOrgId: query.physicalOrgId };
      const contractList = await ctx.model.MedicalExamContract.find(match).select('_id');
      const contractIds = contractList.map(contract => contract._id); // 提取 _id 数组

      // 添加 contractId 到过滤条件中
      filter.contractId = { $in: contractIds };
      // 在 MedicalExamPlan 中查找符合条件的记录，并分页
      const res = await ctx.model.MedicalExamPlan.find(filter)
        .sort({ createdAt: -1 })
        .skip((pageNum - 1) * pageSize)
        .limit(pageSize)
        .populate({
          path: 'healthCheckRegister',
          select: 'status'
        })

      // 统计总数
      const total = await ctx.model.MedicalExamPlan.countDocuments(filter);

      return {
        res,
        total
      };
    } catch (error) {
      console.log(error, '43274');
      throw error; // 抛出错误，方便调试
    }
  }

  // 获取科室基本信息
  async getDepartmentDetail(query) {
    try {
      const { ctx } = this;
      const searchCondition = {};
      if (query.id) {
        searchCondition._id = { _id: query.id };
      }
      const res = await ctx.model.HealthDepartment.findOne(searchCondition)
      let filter = {
        _id: { $in: res.departmentCheckItem || [] }  // 防止departmentCheckItem为undefined
      }
      const allowItem = await ctx.model.HealthCheckProject.find(filter)
      return {
        res,
        allowItem
      }
    } catch (error) {
      // 错误捕获
      console.log(error)
    }
  }

  // 获取科室允许的体检项目
  async getDepartmentAllowItem(query) {
    const { ctx } = this;
    try {
      let filter = {}
      filter._id = { $in: query.departmentCheckItem };
      const res = await ctx.model.HealthCheckItemInfo.find(searchCondition)
      return res
    } catch (error) {
      console.log(error, '=====获取包含的检查项目')
    }
  }

  // 更新科室基本信息
  async updateDepartmentInfo(query) {
    try {
      const { ctx } = this
      await ctx.model.HealthDepartment.updateOne(
        { _id: query._id },
        { $set: { ...query } },
        { upsert: true }
      );
    } catch (error) {
      console.log(error)
    }
  }

  // 获取疑似职业病列表
  async getSuspectDiseaseList(query) {
    const pageNum = Number(query.pageNum);
    const pageSize = Number(query.pageSize);
    const { ctx } = this;
    const filter = {
      status: 3, // 已审核
      jobConclusion: { $in: [3] } // 只查询结论为疑似职业病的记录
    };
    if (query.physicalOrgId) {
      filter.physicalOrgID = query.physicalOrgId;
    }
    if (query.name) {
      filter.name = { $regex: query.name, $options: 'i' };
    }
    if (query.idNumber) {
      filter.idNumber = { $regex: query.idNumber, $options: 'i' };
    }
    if (query.phone) {
      filter.phone = { $regex: query.phone, $options: 'i' };
    }
    const res = await ctx.model.HealthCheckRegister.find(filter)
      .populate({
        path: 'physicalOrgID',
        select: 'name',
      })
      .skip((pageNum - 1) * pageSize)
      .limit(pageSize)
      .sort({ createAt: -1 });
    // 统计总数
    const sum = await ctx.model.HealthCheckRegister.find(filter)
    const total = sum.filter(item => item._id !== null);
    return {
      res,
      total: total.length
    }
  }

  // 疑似职业病上报
  async reportSuspectDisease(query) {
    const { ctx } = this
    await ctx.model.HealthCheckRegister.updateOne(
      { _id: query.id },
      { $set: { reportStatus: 2 } },
      { upsert: true }
    );
  }

  // 获取体检结果异常人员列表
  async getTjExceptionList(query) {
    const pageNum = Number(query.pageNum);
    const pageSize = Number(query.pageSize);
    const { ctx } = this;
    const filter = {};
    if (query.physicalOrgId) {
      filter.physicalOrgID = query.physicalOrgId;
      filter.status = 3
    }
    if (query.name) {
      filter.name = { $regex: query.name, $options: 'i' };
    }
    if (query.idNumber) {
      filter.idNumber = { $regex: query.idNumber, $options: 'i' };
    }
    if (query.phone) {
      filter.phone = { $regex: query.phone, $options: 'i' };
    }
    filter.jobConclusion = {
      $ne: [],  // 数组不为空
      $elemMatch: { $in: [2, 3, 4, 5] }  // 包含指定值中的任意一个或多个
    }
    const res = await ctx.model.HealthCheckRegister.find(filter)
      .populate({
        path: 'physicalOrgID',
        select: 'name',
      })
      .skip((pageNum - 1) * pageSize)
      .limit(pageSize)
      .sort({ createAt: -1 });
    // 统计总数
    const sum = await ctx.model.HealthCheckRegister.find(filter)
    const total = sum.filter(item => item._id !== null);
    return {
      res,
      total: total.length
    }
  }

  // 获取已通过审核但未登记的人员列表
  async getNoHealthCheckList(query) {
    const { ctx } = this;
    const pageNum = Number(query.pageNum) || 1;
    const pageSize = Number(query.pageSize) || 10;
    let filter = {}
    filter.registerCheckNo = { $exists: true, $ne: null }
    if (query.name) {
      filter.name = { $regex: query.name, $options: 'i' };
    }
    const list = await ctx.model.MedicalExamPlan.find(filter)
      .populate({
        path: 'registerCheckNo',
        match: { checkNo: null }, // 故意匹配不存在的条件
        // select: '_id', // 只需要知道是否存在
        // options: { lean: true }
      })
      .skip((pageNum - 1) * pageSize)
      .limit(pageSize)
      .sort({ createdAt: -1 });

    // 总数查询需要同样的逻辑
    const allPlans = await ctx.model.MedicalExamPlan.find(filter).populate({
      path: 'registerCheckNo',
      match: { checkNo: null }
    });

    const total = allPlans.filter(plan => plan._id);

    return {
      list,
      total: total.length,
      pageNum,
      pageSize
    }
  }

  async statisticExamTypeTjCountByYear() {
    const { ctx } = this;

    const pipeline = [
      // 阶段1：筛选有登记时间和检查类型的记录
      {
        $match: {
          registerTime: { $exists: true, $ne: null }, // 必须存在登记时间
          examType: { $exists: true, $ne: null }      // 必须存在检查类型
        }
      },
      // 阶段2：提取年份和检查类型
      {
        $project: {
          year: { $year: "$registerTime" }, // 从登记时间提取年份
          examType: 1                        // 保留检查类型
        }
      },
      // 阶段3：按年份和检查类型分组统计
      {
        $group: {
          _id: {
            year: "$year",
            examType: "$examType"
          },
          count: { $sum: 1 } // 计算每组数量
        }
      },
      // 阶段4：按年份汇总结果
      {
        $group: {
          _id: "$_id.year", // 按年份二次分组
          types: {
            $push: {
              examType: "$_id.examType",
              count: "$count"
            }
          },
          total: { $sum: "$count" } // 每年总人数
        }
      },
      // 阶段5：格式化输出
      {
        $project: {
          year: "$_id",
          types: {
            $map: { // 格式化检查类型输出
              input: "$types",
              as: "type",
              in: {
                examType: "$$type.examType",
                name: { // 添加检查类型中文名
                  $switch: {
                    branches: [
                      { case: { $eq: ["$$type.examType", 1] }, then: "岗前" },
                      { case: { $eq: ["$$type.examType", 2] }, then: "在岗" },
                      { case: { $eq: ["$$type.examType", 3] }, then: "离岗" },
                      { case: { $eq: ["$$type.examType", 4] }, then: "离岗后" },
                      { case: { $eq: ["$$type.examType", 5] }, then: "应急" }
                    ],
                    default: "未知"
                  }
                },
                count: "$$type.count"
              }
            }
          },
          total: 1,
          _id: 0
        }
      },
      // 阶段6：按年份升序排序
      {
        $sort: { "year": 1 }
      }
    ];
    // enum: [1, 2, 3, 4, 5], // 1: 岗前, 2: 在岗, 3: 离岗, 4: 离岗后, 5: 应急
    // { year:'2025年' ,startWorkCount:100 ,workingCount:25 ,offWorkCount:30},
    const rawStats = await ctx.model.HealthCheckRegister.aggregate(pipeline);
    let result = []
    rawStats.forEach(item => {
      let obj = {}
      obj.year = item.year + '年'
      item.types.forEach(ele => {
        if (ele.examType == 1) {
          obj.startWorkCount = ele.count
        }
        if (ele.examType == 2) {
          obj.workingCount = ele.count
        }
        if (ele.examType == 3) {
          obj.offWorkCount = ele.count
        }
      })
      result.push(obj)
    })

    return result
  }

  // 获取已预约负荷列表
  async getAppointedList(params = {}) {
    const { ctx } = this;
    const { pageNum = 1, pageSize = 10, physicalOrgId, date } = params;
    // MedicalExamPlan不记录体检机构id，所以先查当前机构的合同，再查体检计划
    const contracts = await ctx.model.MedicalExamContract.find({ physicalOrgId }).select('_id');
    if (contracts.length === 0) {
      return { list: [], total: 0 }
    }
    const contractIds = contracts.map(c => c._id);
    const filter = {
      contractId: { $in: contractIds },
      reservationStatu: 2, // 审核通过
      reservationDate: { $type: "date" }, // 过滤掉string类型的数据
    }
    if (date) {
      filter.reservationDate = { $gte: new Date(moment(date).startOf('day')), $lt: new Date(moment(date).endOf('day')) }
    } else {
      filter.reservationDate = { $gte: new Date(moment().startOf('day')) }// 大于等于今天0点0分0秒
    }
    const res = await ctx.model.MedicalExamPlan
      .aggregate([
        { $match: filter },
        { $sort: { "reservationDate": 1 } },
        {
          $group: {
            _id: {
              date: { $dateToString: { format: "%Y-%m-%d", date: "$reservationDate" } },
            },
            count: { $sum: 1 },
            // 岗前、在岗、离岗
            preJob: { $sum: { $cond: [{ $eq: ["$examType", 1] }, 1, 0] } },
            onJob: { $sum: { $cond: [{ $eq: ["$examType", 2] }, 1, 0] } },
            offJob: { $sum: { $cond: [{ $eq: ["$examType", 0] }, 1, 0] } }
          }
        },
        {
          $facet: {
            list: [
              // { $sort: { "_id.data": 1 } },
              { $skip: (pageNum - 1) * pageSize },
              { $limit: pageSize }
            ],
            total: [
              { $count: 'total' }
            ]
          }
        },
      ])

    return res
  }

  // 删除职业健康体检项目,假删，将使用状态改为2已删除
  async deleteHealthItem(query) {
    try {
      const { ctx } = this
      await ctx.model.HealthCheckItemInfo.updateOne(
        { _id: query.id },
        { $set: { useStatus: 2 } },
        { upsert: true }
      )
    } catch (error) {
      console.log(error)
    }
  }
}

module.exports = ContractTableService;