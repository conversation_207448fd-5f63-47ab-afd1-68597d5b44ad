/*
 * @Author: 龙城梓
 * @Description: 体检登记
 * @Description: 在体检端同意劳动者的体检预约后,为其创建体检登记信息,生成体检编号
 * 
 */
module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');

  const healthCheckRegisterSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate
    },
    // 体检编号
    checkNo: {
      type: String,
      required: true,
      unique: true
    },
    // 工号(员工_id)
    employeeID: {
      type: String,
      ref: 'Employees'
    },
    // 用人单位ID
    EnterpriseID: {
      type: String,
      ref: 'Adminorg',
    },
    // 体检机构id
    physicalOrgID: {
      type: String,
      ref: 'PhysicalExamOrg',
    },
    // 合同编号
    contractID: { // 对应的合同id
      type: String,
      ref: 'MedicalExamContract',
    },
    // 体检预约ID
    examPlanID: {
      type: String,
      ref: 'MedicalExamPlan',
    },
    // 状态
    // 当体检预约同意后，生成体检登记信息，状态为未登记；
    // 在过程管理中输入基本信息、危害因素、选择项目之后保存为已登记
    // 在过程管理中录入体检结果和小结总结之后保存为已总结
    // 总结过后的体检登记信息需要审核，审核通过后状态为审核通过，审核不通过后状态为审核不通过。审核通过后生成suspect
    status: {
      type: Number,
      enum: [0, 1, 2, 3, 4], // 0 未登记 1 已登记 2 已总结(待审核) 3 审核通过 4 审核不通过
      default: 0
    },
    suspectId: {
      type: String,
      ref: 'suspect'
    },
    // 审核原因
    auditReason: {
      type: String
    },
    // 姓名
    name: {
      type: String,
      required: true
    },
    // 性别
    gender: {
      type: String,
      enum: ['1', '2'], // 1: 男, 2: 女
      // required: true
    },
    // 证件类型
    idType: {
      type: String,
      enum: [1, 2, 3, 4, 5, 6, 7, 99], // 1: 居民身份证, 2: 居民户口簿, 3: 护照, 4: 军官证, 5: 驾驶证, 6: 港澳居民来往内地通行证, 7: 台湾居民来往内地通行证, 99: 其他法定有效证件
      default: 1
    },
    // 证件号
    idNumber: {
      type: String,
      required: true
    },
    // 出生日期
    birthDate: {
      type: Date,
    },
    // 婚姻状况
    maritalStatus: {
      type: String,
      enum: [10, 20, 21, 22, 23, 30, 40, 90], // 10: 未婚, 20: 已婚, 21: 初婚, 22: 再婚, 23: 复婚, 30: 丧偶, 40: 离婚, 90: 未说明的婚姻状况
      default: '10'
    },
    // 手机号码
    phone: {
      type: String,
    },
    // 住址
    address: {
      type: String
    },
    // 紧急联系人
    emergencyContact: {
      type: String
    },
    // 紧急联系人电话
    emergencyPhone: {
      type: String
    },
    // 体检类型
    checkType: {
      type: String,
      enum: ['1', '2'], // 1: 职业健康体检, 2: 一般体检
    },
    // 部门
    department: {
      type: String
    },
    // 工种
    workType: {
      type: String
    },
    // 接触的危害因素
    hazardFactors: [{
      type: String
    }],
    // 体检的危害因素
    checkHazardFactors: [
      {
        _id: {
          type: String,
          default: shortid.generate
        },
        code: String, // 危害因素编码
        name: {
          type: String
        },
        // 职检结论
        // 结论 // 1	目前未见异常 2	复查 3	疑似职业病 4	职业禁忌证 5	其他疾病或异常
        conclusion: [{
          type: Number,
          enum: [1, 2, 3, 4, 5]
        }],
        // 职业病
        disease: Array,
        // 禁忌证
        taboo: Array
      }
    ],
    // 总工龄年
    totalWorkYears: {
      type: Number,
      default: 0
    },
    // 总工龄月
    totalWorkMonths: {
      type: Number,
      default: 0
    },
    // 接触工龄年
    exposureWorkYears: {
      type: Number,
      default: 0
    },
    // 接触工龄月
    exposureWorkMonths: {
      type: Number,
      default: 0
    },
    // 检查类型
    examType: {
      type: String,
      enum: [1, 2, 3, 4, 5], // 1: 岗前, 2: 在岗, 3: 离岗, 4: 离岗后, 5: 应急
    },
    // 是否复查
    isRecheck: {
      type: Boolean,
    },
    recheckNo: {  // 复查的体检编号(上一次的体检编号)
      type: String
    },
    // 复查时间
    recheckTime: {
      type: Date
    },
    // 复查项目
    recheckProjects: [{
      _id: {
        type: String,
        default: shortid.generate
      },
      // 科室id
      departmentId: {
        type: String,
        ref: 'HealthDepartment'
      },
      // 科室名称
      departmentName: {
        type: String
      },
      // 收费项目id
      projectId: {
        type: String,
        ref: 'HealthCheckProject'
      },
      // 项目名称
      projectName: {
        type: String
      },
    }],

    // 项目总价
    totalPrice: {
      type: Number
    },
    // 人脸照片
    facePhoto: {
      type: String
    },

    // 检查科室
    checkDepartments: [
      {
        departmentId: {
          type: String,
          ref: 'HealthDepartment'
        },
        // 科室名称
        departmentName: {
          type: String
        },
        // 收费项目
        checkProjects: [{
          // 收费项目id
          projectId: {
            type: String,
            ref: 'HealthCheckProject'
          },
          // 项目名称
          projectName: {
            type: String
          },
          // 项目价格
          projectPrice: {
            type: Number
          },
          // 检查项目和结果
          checkItems: [{
            // 检查项目id
            itemId: {
              type: String,
              ref: 'HealthCheckItemInfo'
            },
            projectNumber: {    // 检查项目编号
              type: String
            },
            // 项目名称
            itemName: {
              type: String
            },
            // 检测结果
            result: { type: String },
            // 单位
            msrunt: { type: String },
            // 参考范围
            standardValueMin: { type: Number },
            standardValueMax: { type: Number },
            // 结果判定
            conclusion: { type: String },
          }],
        }],

        // 科室小结
        summary: {
          type: String
        },

        status: {
          type: Number,
          enum: [0, 1, 2], // 0 未小结 1 已小结/批量小结 2 强制小结
          default: 0
        },
      }
    ],

    // 健结总结
    healthSummary: {
      type: String
    },
    // 意见建议
    suggestion: {
      type: String
    },
    // 职检总结
    jobSummary: {
      type: String
    },
    // 职检结论
    jobConclusion: {
      type: Array
    },
    // 登记日期
    registerTime: {
      type: Date,
    },
    // 报告生成日期
    reportTime: {
      type: Date
    },

    // 上报状态（疑似职业病上报）
    reportStatus: {
      type: Number,
      enum: [1, 2], // 1为未上报  2为已上报
      default: 1
    },
    // 诊断状态（是否发起诊断
    diagnosisStatus: {
      type: Number,
      enum: [1, 2], // 1为未发起 2为已发起
      default: 1
    },

  }, {
    timestamps: true
  });

  // 创建索引
  healthCheckRegisterSchema.index({ checkNo: 1 }, { unique: true });
  healthCheckRegisterSchema.index({ idNumber: 1 });
  healthCheckRegisterSchema.index({ name: 1 });

  return mongoose.model('HealthCheckRegister', healthCheckRegisterSchema);
}; 