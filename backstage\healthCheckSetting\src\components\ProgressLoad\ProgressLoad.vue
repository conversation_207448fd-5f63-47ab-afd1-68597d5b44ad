<template>
  <div class="loadBox">
    <div class="loadMessage">工作场所导入中...请稍等</div>
    <img src="@/assets/noWord.svg" />
    <el-progress
        :text-inside="true"
        :stroke-width="20"
        :percentage="(percentageItem/tableDataLength)*100"
        :format="format"
        class="progress"
      ></el-progress>
  </div>
</template>

<script>
export default {
  props: {
    percentage : {
      type: Number,
      default: 0
    },
    percentageItem: {
      type: Number,
      default: 0
    },
    tableDataLength: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {

    }
  },
  methods: {
    format(percentage) {
      console.log(this.percentageItem,this.tableDataLength,222222222222222)
      return this.percentageItem + ' / 共' + this.tableDataLength + '条';
    },
  }
}
</script>

<style lang="scss" scoped>
.loadMessage{
  color: #333;
}
  .loadBox {
    width: 300px;
    padding: 20px;
    background: white;
    // position: absolute;
    // left: 50%;
    // top: 50%;
    transform: translate(-50%, 0);
    z-index: 1;
    border-radius: 10px;
    color: #fff;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-shadow: 0 2px 12px 0 #82a3dd;

    img {
      width: 300px;
      margin-top: 30px;
    }
    .progress {
      width: 260px;
    }
  }
  
</style>